# 部署上线指南（零基础实习生可用）

本指南手把手带你把本项目部署到线上，任何人都能访问并正常使用。按照顺序一步步做即可。

---

## 0. 你需要准备什么
- **GitHub 账号**（用于托管源码和连接 Vercel）
- **Vercel 账号**（用于托管前端与 API）
- **Supabase 账号**（用于数据库与对象存储）
- 本地电脑（可选，用于本地验证）：安装 Node.js 18+、Git

---

## 1. 克隆项目到你的 GitHub
1) 打开 GitHub，创建一个新的空仓库（Private 或 Public 均可）。
2) 在本地把现有项目推送到你的新仓库：
```bash
# 在你的电脑上
cd mome-dynamic-manga 项目所在的上层目录
# 如果还没有 git 初始化
git init
# 添加远程仓库（把下面的URL替换成你自己的GitHub仓库地址）
git remote add origin https://github.com/<your-account>/<your-repo>.git
# 添加所有文件并提交
git add .
git commit -m "Initial deployment"
# 推送到GitHub主分支
git branch -M main
git push -u origin main
```

提示：如果你的项目在一个更大的 monorepo 中，请只把 `mome-dynamic-manga` 子项目单独作为仓库推送会更简单。

---

## 2. 创建 Supabase 项目
1) 登录 Supabase 控制台，新建 Project。
2) 选区域、设置数据库密码并等待初始化完成。
3) 进入 `Project Settings -> API`，记录：
   - Project URL（形如 https://xxxxx.supabase.co）
   - anon public key（匿名公钥）
   - service_role key（服务角色密钥，必须保密，仅服务器端用）
4) 打开 `Storage`，后续我们会自动创建存储桶（bucket），也可以手动创建名为 `storyboard-images` 的公开桶。

---

## 3. 配置数据库 Schema（可选但推荐）
本项目已经提供 SQL 脚本，支持一键初始化。你可以二选一：

- 方案A：在 Supabase SQL Editor 中执行脚本
  1) 打开 Supabase 的 SQL Editor
  2) 找到本仓库 `mome-dynamic-manga/database/` 目录中的脚本：
     - `complete_rebuild.sql`（完整重建，最全）或 `simple_setup.sql`（最小可用）
  3) 复制 SQL 内容到 Supabase SQL Editor，执行

- 方案B：用项目自带脚本（需要在本地一次性执行）
  1) 在本地创建 `.env.local`（参考第 5 步的环境变量）
  2) 运行：
  ```bash
  cd mome-dynamic-manga
  npm install
  node scripts/run-migration.js
  ```

---

## 4. 开通存储桶（Storage Bucket）
- 本项目在首次上传图片时会自动检查并创建 `storyboard-images` 存储桶。
- 如果你想手动创建：在 Supabase -> Storage -> New bucket，输入 `storyboard-images`，勾选 Public。

---

## 5. 准备环境变量
在本地和 Vercel 都需要配置这些变量。

必填：
- `NEXT_PUBLIC_SUPABASE_URL`：Supabase 项目 URL
- `NEXT_PUBLIC_SUPABASE_ANON_KEY`：Supabase 匿名公钥（前端可用）

强烈建议（用于服务端管理与存储写入）：
- `SUPABASE_SERVICE_ROLE_KEY`：服务角色密钥（只放服务器环境，切勿暴露到前端）

AI 相关（至少配置其一，否则相关功能不可用）：
- `OPENROUTER_API_KEY`：OpenRouter API Key（用于 LLM / 文生图代理）
- `GEMINI_API_KEY`：Google Gemini API Key（用于图像生成/处理）

可选：
- `NEXTAUTH_URL`：用于回传引用头，未使用 NextAuth 也可省略

示例 `.env.local`（本地开发放在 `mome-dynamic-manga/.env.local`）：
```env
NEXT_PUBLIC_SUPABASE_URL=https://xxxxxx.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOi...
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOi...
OPENROUTER_API_KEY=sk-or-...
GEMINI_API_KEY=AIza...
```

在 Vercel 配置相同的环境变量（不需要加 NEXT_PUBLIC 的会自动仅服务端可见）。

---

## 6. 本地快速验证（可选）
```bash
cd mome-dynamic-manga
npm install
npm run dev
# 打开 http://localhost:3000 访问
```
- 打开首页与 `/projects`、`/storyboard`、`/image-editor` 等页面，简单走通流程。
- 访问 `http://localhost:3000/api/fix-database` 或使用 `Fix Database` 页面检查后端联通性（如有）。

---

## 7. 用 Vercel 一键部署
1) 登录 Vercel，点击 New Project -> Import Git Repository，选择你的 GitHub 仓库。
2) Framework 选择 Next.js。Root Directory 指向 `mome-dynamic-manga`（如果你的仓库根目录就是它则保持默认）。
3) Build & Output Settings：
   - Build Command: `npm run build`
   - Output Directory: `.next`
   - Install Command: `npm install`
4) 在 Vercel 的 Project Settings -> Environment Variables，添加第 5 步中的所有变量。
5) 点击 Deploy，等待完成。Vercel 会给出一个域名，如 `https://your-app.vercel.app`。

注意：
- 确保 `SUPABASE_SERVICE_ROLE_KEY` 只配置在 Vercel（服务器端），不要放在前端公开区域。
- 首次上传图片时如提示没有存储桶，稍等或手动创建 `storyboard-images`。

---

## 8. 上线后功能自检清单
- 访问首页：能正常打开，无明显错误。
- `/projects`：能创建/查看项目。
- `/storyboard`：
  - 能创建分镜条目并保存到数据库。
  - 如有图片，能上传并显示（公共 URL 正常）。
- `/image-editor`：
  - 画布、工具栏可用；截图/上传功能可用。
  - 如果配置了 `OPENROUTER_API_KEY` 或 `GEMINI_API_KEY`，AI 修改/生成可用。
- 后端 API：访问 `/api/storyboard`、`/api/generate-image` 返回 200 或合理的错误提示。

---

## 9. 常见问题排查
- 访问报错 500，日志提示“Supabase 环境变量未正确配置”
  - 检查 Vercel 环境变量是否填写完整：`NEXT_PUBLIC_SUPABASE_URL`、`NEXT_PUBLIC_SUPABASE_ANON_KEY`、`SUPABASE_SERVICE_ROLE_KEY`
- 图片无法上传/显示
  - 确认 `storyboard-images` 桶存在，且为 Public；
  - 后端日志中无权限错误；如果是私有桶，需配置签名 URL；
  - 检查服务端是否有 `SUPABASE_SERVICE_ROLE_KEY`。
- AI 相关接口返回“未配置密钥”
  - 至少配置 `OPENROUTER_API_KEY` 或 `GEMINI_API_KEY` 其中之一。
- CORS 或 Referer 相关问题
  - Vercel 部署域名与配置的引用头不一致通常不影响，但可在 `aiProcessor.ts` 等处调整 `HTTP-Referer`。

---

## 10. 更新与回滚
- 有改动直接 push 到 GitHub 的 `main` 分支，Vercel 会自动重新部署。
- 如果新版有问题，在 Vercel 的 Deployments 页面选择上一个部署进行回滚（Promote）。

---

## 11. 安全与成本注意
- `SUPABASE_SERVICE_ROLE_KEY` 必须只放在服务器端环境变量，不要暴露到浏览器。
- 存储为公开桶便于直接访问，但敏感图片请使用私有桶并改为签名 URL 访问。
- AI 接口按量计费，务必设置用量告警与配额。

---

完成以上步骤，你的站点就已经对外可访问了。如果需要，我可以为你录制一版带截图的详细版（放到本文件或 Wiki）。

