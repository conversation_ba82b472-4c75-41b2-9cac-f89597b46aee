import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

export async function POST(request: NextRequest) {
  try {
    console.log('🔧 开始修复数据库表结构...');
    
    // 检查当前表结构
    const { data: columns, error: columnError } = await supabase
      .from('information_schema.columns')
      .select('column_name, data_type')
      .eq('table_name', 'chapters');
    
    if (columnError) {
      console.error('❌ 无法查询表结构:', columnError);
      return NextResponse.json({ 
        success: false, 
        error: '无法查询表结构',
        details: columnError 
      });
    }
    
    console.log('📋 当前表结构:', columns);
    
    // 检查是否缺少字段
    const columnNames = columns?.map(col => col.column_name) || [];
    const missingColumns = [];
    
    if (!columnNames.includes('start_index')) {
      missingColumns.push('start_index');
    }
    
    if (!columnNames.includes('end_index')) {
      missingColumns.push('end_index');
    }
    
    if (missingColumns.length === 0) {
      return NextResponse.json({
        success: true,
        message: '表结构正常，无需修复',
        columns: columnNames
      });
    }
    
    console.log('⚠️  缺少字段:', missingColumns);
    
    // 尝试添加缺失字段
    const alterStatements = [];
    
    if (missingColumns.includes('start_index')) {
      alterStatements.push('ALTER TABLE chapters ADD COLUMN start_index INTEGER DEFAULT 0');
    }
    
    if (missingColumns.includes('end_index')) {
      alterStatements.push('ALTER TABLE chapters ADD COLUMN end_index INTEGER DEFAULT 0');
    }
    
    // 执行ALTER语句
    for (const statement of alterStatements) {
      console.log('📝 执行SQL:', statement);
      
      const { error: alterError } = await supabase.rpc('exec_sql', {
        sql: statement
      });
      
      if (alterError) {
        console.error('❌ SQL执行失败:', alterError);
        return NextResponse.json({
          success: false,
          error: 'SQL执行失败',
          details: alterError,
          suggestion: '请在Supabase控制台手动执行以下SQL：',
          sql: alterStatements
        });
      }
    }
    
    // 更新现有记录
    const { error: updateError } = await supabase
      .from('chapters')
      .update({
        start_index: 0,
        end_index: 0
      })
      .is('start_index', null);
    
    if (updateError) {
      console.warn('⚠️  更新现有记录失败:', updateError);
    }
    
    return NextResponse.json({
      success: true,
      message: '数据库表结构修复成功',
      addedColumns: missingColumns,
      sql: alterStatements
    });
    
  } catch (error) {
    console.error('💥 修复过程出错:', error);
    return NextResponse.json({
      success: false,
      error: '修复过程出错',
      details: error instanceof Error ? error.message : String(error)
    });
  }
}

export async function GET() {
  try {
    // 检查表结构状态
    const { data: columns, error } = await supabase
      .from('information_schema.columns')
      .select('column_name, data_type, is_nullable, column_default')
      .eq('table_name', 'chapters')
      .order('ordinal_position');
    
    if (error) {
      return NextResponse.json({ 
        success: false, 
        error: '无法查询表结构',
        details: error 
      });
    }
    
    const columnNames = columns?.map(col => col.column_name) || [];
    const hasStartIndex = columnNames.includes('start_index');
    const hasEndIndex = columnNames.includes('end_index');
    
    return NextResponse.json({
      success: true,
      tableExists: columns && columns.length > 0,
      columns: columns,
      hasStartIndex,
      hasEndIndex,
      needsFix: !hasStartIndex || !hasEndIndex,
      missingColumns: [
        ...(!hasStartIndex ? ['start_index'] : []),
        ...(!hasEndIndex ? ['end_index'] : [])
      ]
    });
    
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: '检查失败',
      details: error instanceof Error ? error.message : String(error)
    });
  }
}
