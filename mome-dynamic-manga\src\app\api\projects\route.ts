import { NextRequest, NextResponse } from 'next/server';
import { parseFile } from '@/lib/fileParser';
import { splitIntoChapters } from '@/lib/chapterSplitter';
import { ProjectService, ChapterService } from '@/lib/databaseService';
import { testConnection, DatabaseError } from '@/lib/supabase';

// 生成唯一ID
function generateId(): string {
  return Date.now().toString() + Math.random().toString(36).substr(2, 9);
}

// 简化的文件解析器（保留原有逻辑）
class SimpleFileParser {
  static validateFile(file: File): { valid: boolean; error?: string } {
    const maxSize = 20 * 1024 * 1024; // 20MB
    const allowedTypes = ['txt', 'docx'];
    const extension = file.name.split('.').pop()?.toLowerCase();

    if (!extension || !allowedTypes.includes(extension)) {
      return {
        valid: false,
        error: '不支持的文件格式。请上传 .txt 或 .docx 文件。',
      };
    }

    if (file.size > maxSize) {
      return {
        valid: false,
        error: '文件大小超过限制。请上传小于 20MB 的文件。',
      };
    }

    return { valid: true };
  }

  static async parseFile(file: File): Promise<{ success: boolean; content: string; wordCount: number; error?: string }> {
    try {
      const extension = file.name.split('.').pop()?.toLowerCase();
      let content: string;

      if (extension === 'txt') {
        content = await this.parseTxt(file);
      } else if (extension === 'docx') {
        // 简化处理，暂时只支持txt
        return {
          success: false,
          content: '',
          wordCount: 0,
          error: 'DOCX格式暂时不支持，请使用TXT格式'
        };
      } else {
        throw new Error('不支持的文件格式');
      }

      const wordCount = this.calculateWordCount(content);
      return {
        success: true,
        content,
        wordCount
      };
    } catch (error) {
      return {
        success: false,
        content: '',
        wordCount: 0,
        error: error instanceof Error ? error.message : '文件解析失败'
      };
    }
  }

  private static async parseTxt(file: File): Promise<string> {
    const arrayBuffer = await file.arrayBuffer();

    // 尝试多种编码
    const encodings = ['utf-8', 'gbk', 'gb2312', 'big5'];

    for (const encoding of encodings) {
      try {
        const decoder = new TextDecoder(encoding, { fatal: true });
        const content = decoder.decode(arrayBuffer);

        // 检查是否包含有效的中文字符
        const hasValidChinese = /[\u4e00-\u9fff]/.test(content);
        const hasReplacementChar = content.includes('�');

        if (!hasReplacementChar && (hasValidChinese || encoding === 'utf-8')) {
          return content;
        }
      } catch (error) {
        // 继续尝试下一个编码
        continue;
      }
    }

    // 如果所有编码都失败，使用UTF-8并忽略错误
    const decoder = new TextDecoder('utf-8', { fatal: false });
    return decoder.decode(arrayBuffer);
  }

  private static calculateWordCount(text: string): number {
    const cleanText = text.replace(/\s+/g, ' ').trim();
    const chineseChars = (cleanText.match(/[\u4e00-\u9fff]/g) || []).length;
    const englishWords = (cleanText.match(/[a-zA-Z]+/g) || []).length;
    return chineseChars + englishWords;
  }

  static estimateChapterCount(content: string): number {
    const patterns = [
      /第[一二三四五六七八九十\d]+章/g,
      /Chapter\s+\d+/gi,
      /第\d+话/g,
      /第\d+集/g,
    ];

    let maxMatches = 0;
    for (const pattern of patterns) {
      const matches = content.match(pattern);
      if (matches && matches.length > maxMatches) {
        maxMatches = matches.length;
      }
    }

    return maxMatches > 0 ? maxMatches : Math.ceil(this.calculateWordCount(content) / 3000);
  }
}

// 数据库连接检查
async function checkDatabaseConnection(): Promise<{ connected: boolean; error?: string }> {
  try {
    const connected = await testConnection();
    return { connected };
  } catch (error) {
    return {
      connected: false,
      error: error instanceof Error ? error.message : '数据库连接失败'
    };
  }
}

// 创建项目
export async function POST(request: NextRequest) {
  try {
    // 检查数据库连接
    const dbCheck = await checkDatabaseConnection();
    if (!dbCheck.connected) {
      return NextResponse.json(
        {
          success: false,
          error: '数据库连接失败，请检查Supabase配置',
          details: dbCheck.error
        },
        { status: 500 }
      );
    }

    const formData = await request.formData();
    const file = formData.get('file') as File;
    const name = formData.get('name') as string;
    const description = formData.get('description') as string;

    if (!file || !name) {
      return NextResponse.json(
        { success: false, error: '缺少必要参数' },
        { status: 400 }
      );
    }

    // 验证文件
    const validation = SimpleFileParser.validateFile(file);
    if (!validation.valid) {
      return NextResponse.json(
        { success: false, error: validation.error },
        { status: 400 }
      );
    }

    // 解析文件内容
    const parseResult = await SimpleFileParser.parseFile(file);
    if (!parseResult.success) {
      return NextResponse.json(
        { success: false, error: parseResult.error },
        { status: 400 }
      );
    }

    // 估算章节数
    const estimatedChapters = SimpleFileParser.estimateChapterCount(parseResult.content);

    // 创建项目记录
    const projectData = {
      id: generateId(),
      name: name.trim(),
      description: description?.trim() || '',
      original_content: parseResult.content,
      status: 'uploaded'
    };

    // 保存到数据库
    const project = await ProjectService.create(projectData);

    // 自动分割章节并保存
    const chapters = splitIntoChapters(parseResult.content);
    if (chapters.length > 0) {
      const chapterData = chapters.map((chapter, index) => ({
        id: generateId(),
        project_id: project.id,
        chapter_number: index + 1,
        title: chapter.title,
        content: chapter.content,
        word_count: SimpleFileParser.calculateWordCount(chapter.content)
      }));

      await ChapterService.createBatch(chapterData);

      // 更新项目状态
      await ProjectService.update(project.id, { status: 'chapters_split' });
    }

    return NextResponse.json({
      success: true,
      data: {
        project,
        fileContent: parseResult.content,
        wordCount: parseResult.wordCount,
        estimatedChapters,
        chaptersCreated: chapters.length
      }
    });

  } catch (error) {
    console.error('创建项目失败:', error);

    if (error instanceof DatabaseError) {
      return NextResponse.json(
        { success: false, error: error.message },
        { status: 500 }
      );
    }

    return NextResponse.json(
      { success: false, error: '服务器内部错误' },
      { status: 500 }
    );
  }
}

// 获取项目列表或单个项目
export async function GET(request: NextRequest) {
  try {
    // 检查数据库连接
    const dbCheck = await checkDatabaseConnection();
    if (!dbCheck.connected) {
      return NextResponse.json(
        {
          success: false,
          error: '数据库连接失败，请检查Supabase配置',
          details: dbCheck.error
        },
        { status: 500 }
      );
    }

    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');
    const id = searchParams.get('id');

    // 根据ID获取单个项目
    if (action === 'get' && id) {
      const project = await ProjectService.getById(id);
      if (!project) {
        return NextResponse.json(
          { success: false, error: '项目不存在' },
          { status: 404 }
        );
      }

      return NextResponse.json({
        success: true,
        data: project
      });
    }

    // 获取项目列表
    const projects = await ProjectService.getAll();

    return NextResponse.json({
      success: true,
      data: projects
    });

  } catch (error) {
    console.error('获取项目失败:', error);

    if (error instanceof DatabaseError) {
      return NextResponse.json(
        { success: false, error: error.message },
        { status: 500 }
      );
    }

    return NextResponse.json(
      { success: false, error: '服务器内部错误' },
      { status: 500 }
    );
  }
}
