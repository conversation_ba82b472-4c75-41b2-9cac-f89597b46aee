import { NextRequest, NextResponse } from 'next/server';
import { StoryboardEditor, EditableStoryboardItem, ImageGenerationConfig } from '@/lib/storyboardEditor';
import { StoryboardItem } from '@/lib/scriptGenerator';
import { generateStoryboardImage } from '@/lib/imageGenerator';
import { StoryboardService } from '@/lib/databaseService';
import { ImageStorageService } from '@/lib/imageStorage';
import { supabase } from '@/lib/supabase';

// 保存分镜表数据
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { projectId, storyboardItems, action } = body;

    // 验证必要参数
    if (!projectId) {
      return NextResponse.json(
        { success: false, error: '缺少项目ID' },
        { status: 400 }
      );
    }

    switch (action) {
      case 'save':
        return await handleSaveStoryboard(projectId, storyboardItems);
      
      case 'generate_image':
        return await handleGenerateImage(body);
      
      case 'update_item':
        return await handleUpdateItem(projectId, body.itemId, body.updates);
      
      case 'add_item':
        return await handleAddItem(projectId, body.newItem, body.afterId);
      
      case 'delete_item':
        return await handleDeleteItem(projectId, body.itemId);
      
      case 'reorder_items':
        return await handleReorderItems(projectId, body.fromIndex, body.toIndex);
      
      default:
        return NextResponse.json(
          { success: false, error: '未知的操作类型' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('分镜表操作失败:', error);
    
    return NextResponse.json(
      { 
        success: false, 
        error: '操作失败',
        details: process.env.NODE_ENV === 'development' ? error?.toString() : undefined
      },
      { status: 500 }
    );
  }
}

// 获取分镜表配置和模板
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');

    // 检查是否有项目ID查询
    const projectId = searchParams.get('projectId');

    console.log('🔍 Storyboard API GET请求:', { action, projectId });

    switch (action) {
      case 'get':
        if (!projectId) {
          return NextResponse.json(
            { success: false, error: '缺少项目ID' },
            { status: 400 }
          );
        }

        try {
          const storyboards = await StoryboardService.getByProjectId(projectId);
          return NextResponse.json({
            success: true,
            data: storyboards
          });
        } catch (error) {
          console.error('获取分镜表失败:', error);
          return NextResponse.json(
            { success: false, error: '获取分镜表失败' },
            { status: 500 }
          );
        }

      case 'get_by_project':
        if (!projectId) {
          return NextResponse.json(
            { success: false, error: '缺少项目ID' },
            { status: 400 }
          );
        }

        try {
          console.log('🔍 直接查询分镜表，项目ID:', projectId);

          // 直接查询storyboards表
          const { data: storyboards, error } = await supabase
            .from('storyboards')
            .select('*')
            .eq('project_id', projectId)
            .order('created_at', { ascending: false });

          if (error) {
            console.error('❌ 查询分镜表失败:', error);
            throw error;
          }

          console.log('📊 查询到分镜数据:', storyboards?.length || 0, '条');

          // 处理数据，添加统计信息
          const processedStoryboards = (storyboards || []).map((storyboard: any) => ({
            ...storyboard,
            total_panels: storyboard.panels ? storyboard.panels.length : 0,
            total_images: storyboard.panels ?
              storyboard.panels.filter((panel: any) => panel.imageUrl && panel.imageUrl.trim() !== '').length : 0
          }));

          return NextResponse.json({
            success: true,
            data: processedStoryboards
          });
        } catch (error) {
          console.error('❌ 获取分镜表失败:', error);
          return NextResponse.json(
            { success: false, error: '获取分镜表失败', details: error },
            { status: 500 }
          );
        }

      case 'templates':
        return NextResponse.json({
          success: true,
          data: {
            templates: StoryboardEditor.getTemplates(),
            shotTypes: [
              { id: 'extreme-wide', name: '极远景', description: '展示环境全貌' },
              { id: 'wide', name: '远景', description: '建立场景' },
              { id: 'medium', name: '中景', description: '展示角色互动' },
              { id: 'close-up', name: '近景', description: '突出表情' },
              { id: 'extreme-close-up', name: '特写', description: '强调细节' }
            ],
            cameraAngles: [
              { id: 'eye-level', name: '平视', description: '自然视角' },
              { id: 'high-angle', name: '俯视', description: '营造压抑感' },
              { id: 'low-angle', name: '仰视', description: '突出威严' },
              { id: 'bird-eye', name: '鸟瞰', description: '全局视角' },
              { id: 'worm-eye', name: '虫视', description: '极端仰视' }
            ]
          }
        });

      case 'image_config':
        return NextResponse.json({
          success: true,
          data: {
            providers: [
              { id: 'openai', name: 'DALL-E 3', description: '高质量图像生成' },
              { id: 'stability', name: 'Stable Diffusion', description: '开源图像生成' }
            ],
            styles: [
              { id: 'manga', name: '漫画风格', description: '日式漫画风格' },
              { id: 'anime', name: '动画风格', description: '动画片风格' },
              { id: 'realistic', name: '写实风格', description: '真实照片风格' },
              { id: 'sketch', name: '素描风格', description: '手绘素描风格' }
            ],
            aspectRatios: [
              { id: '16:9', name: '16:9', description: '宽屏格式' },
              { id: '4:3', name: '4:3', description: '标准格式' },
              { id: '1:1', name: '1:1', description: '正方形' },
              { id: '9:16', name: '9:16', description: '竖屏格式' }
            ]
          }
        });

      default:
        return NextResponse.json({
          success: true,
          data: {
            message: '分镜表API服务正常运行',
            availableActions: ['save', 'generate_image', 'update_item', 'add_item', 'delete_item', 'reorder_items'],
            availableQueries: ['templates', 'image_config']
          }
        });
    }

  } catch (error) {
    console.error('获取分镜表配置失败:', error);
    
    return NextResponse.json(
      { 
        success: false, 
        error: '获取配置失败',
        details: process.env.NODE_ENV === 'development' ? error?.toString() : undefined
      },
      { status: 500 }
    );
  }
}

// 处理保存分镜表
async function handleSaveStoryboard(projectId: string, storyboardItems: EditableStoryboardItem[]) {
  try {
    // 验证分镜表数据
    const validationErrors = validateStoryboardItems(storyboardItems);
    if (validationErrors.length > 0) {
      return NextResponse.json(
        { success: false, error: '数据验证失败', details: validationErrors },
        { status: 400 }
      );
    }

    // 保存到数据库
    console.log('保存分镜表:', {
      projectId,
      itemCount: storyboardItems.length,
      totalDuration: storyboardItems.reduce((sum, item) => sum + item.duration, 0)
    });

    // 🖼️ 处理分镜图片 - 尝试上传到Storage，失败则保存Base64
    console.log('🔄 开始处理分镜图片...');
    console.log(`📊 分镜项目数量: ${storyboardItems.length}`);

    const processedItems = [...storyboardItems]; // 复制数组避免修改原数据
    const itemsWithImages = processedItems.filter(item => item.imageUrl && item.imageUrl.startsWith('data:image/'));

    console.log(`🖼️ 发现 ${itemsWithImages.length} 个包含图片的分镜项目`);

    if (itemsWithImages.length > 0) {
      console.log('🔄 尝试上传图片到Supabase Storage...');

      for (const item of itemsWithImages) {
        if (!item.imageUrl) continue;

        try {
          // 尝试上传到Storage
          const fileName = `storyboards/${Date.now()}_${projectId}_${item.id}.png`;
          console.log(`🔄 开始上传图片: ${item.id} -> ${fileName}`);

          const storageUrl = await ImageStorageService.uploadBase64Image(item.imageUrl, fileName);

          if (storageUrl && storageUrl.startsWith('http')) {
            // 上传成功，使用Storage URL
            item.imageUrl = storageUrl;
            console.log(`✅ 图片上传Storage成功: ${item.id}`);
            console.log(`📎 Storage URL: ${storageUrl}`);
          } else {
            // 上传失败，保持Base64格式
            console.log(`⚠️ 图片Storage上传失败，保持Base64格式: ${item.id}`);
            console.log(`📊 返回值: ${storageUrl}`);
          }
        } catch (uploadError) {
          console.log(`⚠️ 图片Storage上传异常，保持Base64格式: ${item.id}`, uploadError);
        }
      }
    }

    console.log('✅ 图片处理完成');

    // 使用新的数据库函数保存分镜数据
    console.log('💾 调用数据库函数保存分镜数据...');

    const { data: storyboardId, error } = await supabase.rpc('update_storyboard_panels', {
      p_project_id: projectId,
      p_panels: processedItems
    });

    if (error) {
      console.error('❌ 数据库函数调用失败:', error);
      throw new Error(`保存分镜数据失败: ${error.message}`);
    }

    console.log('✅ 分镜数据保存成功，分镜ID:', storyboardId);

    // 计算统计信息
    const imagesCount = processedItems.filter(item => item.imageUrl && item.imageUrl.trim() !== '').length;

    const savedStoryboard = {
      id: storyboardId,
      project_id: projectId,
      panels: processedItems,
      total_panels: processedItems.length,
      total_images: imagesCount,
      style_settings: {
        total_duration: processedItems.reduce((sum, item) => sum + item.duration, 0),
        scene_count: new Set(processedItems.map(item => item.sceneNumber)).size,
        shot_count: processedItems.length
      }
    };

    return NextResponse.json({
      success: true,
      data: {
        message: '分镜表保存成功',
        projectId,
        storyboardId: savedStoryboard.id,
        itemCount: storyboardItems.length,
        savedAt: new Date().toISOString()
      }
    });

  } catch (error) {
    throw new Error(`保存分镜表失败: ${error}`);
  }
}

// 处理图像生成
async function handleGenerateImage(body: any) {
  try {
    const { itemId, visualPrompt, config, projectId } = body;

    if (!itemId || !visualPrompt) {
      return NextResponse.json(
        { success: false, error: '缺少必要参数：itemId 和 visualPrompt' },
        { status: 400 }
      );
    }

    console.log('🎨 开始生成分镜图像:', {
      itemId,
      prompt: visualPrompt.substring(0, 50) + '...',
      config,
      projectId
    });

    // 检查 Gemini API 密钥
    if (!process.env.GEMINI_API_KEY) {
      console.error('❌ 缺少 GEMINI_API_KEY 环境变量');
      return NextResponse.json(
        {
          success: false,
          error: 'API 配置错误：缺少 Gemini API 密钥',
          details: '请在环境变量中设置 GEMINI_API_KEY'
        },
        { status: 500 }
      );
    }

    // 使用 Gemini 2.5 Flash Image API 生成图像
    const result = await generateStoryboardImage(visualPrompt, {
      style: config?.style || 'manga',
      aspectRatio: config?.aspectRatio || '16:9',
      quality: config?.quality || 'high',
      ...config
    });

    if (result.success) {
      console.log('✅ 分镜图像生成成功');

      return NextResponse.json({
        success: true,
        data: {
          itemId,
          imageUrl: result.imageUrl,
          imageData: result.imageData,
          status: 'generated',
          generatedAt: new Date().toISOString(),
          metadata: result.metadata
        }
      });
    } else {
      console.error('❌ 分镜图像生成失败:', result.error);

      return NextResponse.json(
        {
          success: false,
          error: result.error || '图像生成失败',
          itemId,
          metadata: result.metadata
        },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('❌ 图像生成异常:', error);
    throw new Error(`图像生成失败: ${error}`);
  }
}

// 处理更新分镜项目
async function handleUpdateItem(projectId: string, itemId: string, updates: Partial<EditableStoryboardItem>) {
  try {
    console.log('更新分镜项目:', { projectId, itemId, updates });

    return NextResponse.json({
      success: true,
      data: {
        message: '分镜项目更新成功',
        itemId,
        updatedAt: new Date().toISOString()
      }
    });

  } catch (error) {
    throw new Error(`更新分镜项目失败: ${error}`);
  }
}

// 处理添加分镜项目
async function handleAddItem(projectId: string, newItem: EditableStoryboardItem, afterId?: string) {
  try {
    console.log('添加分镜项目:', { projectId, newItem: newItem.id, afterId });

    return NextResponse.json({
      success: true,
      data: {
        message: '分镜项目添加成功',
        itemId: newItem.id,
        addedAt: new Date().toISOString()
      }
    });

  } catch (error) {
    throw new Error(`添加分镜项目失败: ${error}`);
  }
}

// 处理删除分镜项目
async function handleDeleteItem(projectId: string, itemId: string) {
  try {
    console.log('删除分镜项目:', { projectId, itemId });

    return NextResponse.json({
      success: true,
      data: {
        message: '分镜项目删除成功',
        itemId,
        deletedAt: new Date().toISOString()
      }
    });

  } catch (error) {
    throw new Error(`删除分镜项目失败: ${error}`);
  }
}

// 处理重新排序
async function handleReorderItems(projectId: string, fromIndex: number, toIndex: number) {
  try {
    console.log('重新排序分镜项目:', { projectId, fromIndex, toIndex });

    return NextResponse.json({
      success: true,
      data: {
        message: '分镜项目排序成功',
        fromIndex,
        toIndex,
        reorderedAt: new Date().toISOString()
      }
    });

  } catch (error) {
    throw new Error(`重新排序失败: ${error}`);
  }
}

// 验证分镜表数据
function validateStoryboardItems(items: EditableStoryboardItem[]): string[] {
  const errors: string[] = [];

  if (!Array.isArray(items)) {
    errors.push('分镜表数据必须是数组');
    return errors;
  }

  if (items.length === 0) {
    errors.push('分镜表不能为空');
    return errors;
  }

  items.forEach((item, index) => {
    const itemErrors = StoryboardEditor.validateStoryboardItem(item);
    if (itemErrors.length > 0) {
      errors.push(`第${index + 1}个分镜项目: ${itemErrors.join(', ')}`);
    }
  });

  return errors;
}
