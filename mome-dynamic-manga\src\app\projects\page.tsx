'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { PageContainer } from '@/components/layout/PageContainer';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { DatabaseStatus } from '@/components/common/DatabaseStatus';
import { ProjectService } from '@/lib/databaseService';
import { 
  BookOpen, 
  Plus, 
  Search, 
  Calendar,
  FileText,
  Trash2,
  Edit,
  Play,
  Clock,
  ArrowRight
} from 'lucide-react';

interface Project {
  id: string;
  name: string;
  description: string;
  total_words: number;
  estimated_chapters: number;
  status: string;
  created_at: string;
  updated_at: string;
}

const statusMap = {
  'uploaded': { label: '已上传', color: 'bg-blue-500' },
  'chapters_split': { label: '章节已分割', color: 'bg-green-500' },
  'story_designed': { label: '剧情已设计', color: 'bg-purple-500' },
  'script_generated': { label: '剧本已生成', color: 'bg-orange-500' },
  'storyboard_completed': { label: '分镜已完成', color: 'bg-pink-500' }
};

export default function ProjectsPage() {
  const router = useRouter();
  const [projects, setProjects] = useState<Project[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filteredProjects, setFilteredProjects] = useState<Project[]>([]);

  useEffect(() => {
    loadProjects();
  }, []);

  useEffect(() => {
    // 过滤项目
    const filtered = projects.filter(project =>
      project.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      project.description.toLowerCase().includes(searchTerm.toLowerCase())
    );
    setFilteredProjects(filtered);
  }, [projects, searchTerm]);

  const loadProjects = async () => {
    try {
      const projectsData = await ProjectService.getAll();
      setProjects(projectsData);
    } catch (error) {
      console.error('加载项目失败:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteProject = async (projectId: string) => {
    if (!confirm('确定要删除这个项目吗？此操作不可恢复。')) {
      return;
    }

    try {
      await ProjectService.delete(projectId);
      await loadProjects(); // 重新加载项目列表
    } catch (error) {
      console.error('删除项目失败:', error);
      alert('删除项目失败，请重试');
    }
  };

  const getNextStep = (status: string) => {
    switch (status) {
      case 'uploaded':
        return { path: '/chapters', label: '章节分割' };
      case 'chapters_split':
        return { path: '/story-design', label: 'AI剧情设计' };
      case 'story_designed':
        return { path: '/script', label: '剧本生成' };
      case 'script_generated':
        return { path: '/storyboard', label: '分镜编辑' };
      case 'storyboard_completed':
        return { path: '/preview', label: '预览作品' };
      default:
        return { path: '/chapters', label: '继续编辑' };
    }
  };

  const formatDate = (dateString: string | undefined) => {
    if (!dateString) return '未知时间';

    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) return '无效日期';

      return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch (error) {
      console.error('日期格式化错误:', error);
      return '日期错误';
    }
  };

  if (isLoading) {
    return (
      <PageContainer>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">加载项目中...</p>
          </div>
        </div>
      </PageContainer>
    );
  }

  return (
    <PageContainer>
      <div className="space-y-6">
        {/* 页面标题和操作 */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 flex items-center space-x-2">
              <BookOpen className="w-6 h-6" />
              <span>项目管理</span>
            </h1>
            <p className="text-gray-600 mt-1">管理您的动态漫画创作项目</p>
          </div>
          
          <Button 
            onClick={() => router.push('/upload')}
            className="flex items-center space-x-2"
          >
            <Plus className="w-4 h-4" />
            <span>新建项目</span>
          </Button>
        </div>

        {/* 数据库状态 */}
        <DatabaseStatus showDetails={false} />

        {/* 搜索栏 */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <Input
            placeholder="搜索项目名称或描述..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>

        {/* 项目列表 */}
        {filteredProjects.length === 0 ? (
          <Card>
            <CardContent className="text-center py-12">
              <BookOpen className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                {projects.length === 0 ? '还没有项目' : '没有找到匹配的项目'}
              </h3>
              <p className="text-gray-600 mb-4">
                {projects.length === 0 
                  ? '开始创建您的第一个动态漫画项目吧！' 
                  : '尝试使用不同的搜索关键词'
                }
              </p>
              {projects.length === 0 && (
                <Button onClick={() => router.push('/upload')}>
                  <Plus className="w-4 h-4 mr-2" />
                  创建新项目
                </Button>
              )}
            </CardContent>
          </Card>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredProjects.map((project) => {
              const status = statusMap[project.status as keyof typeof statusMap] || statusMap.uploaded;
              const nextStep = getNextStep(project.status);
              
              return (
                <Card key={project.id} className="hover:shadow-lg transition-shadow">
                  <CardHeader className="pb-3">
                    <div className="flex justify-between items-start">
                      <CardTitle className="text-lg line-clamp-2">{project.name}</CardTitle>
                      <Badge className={`${status.color} text-white text-xs`}>
                        {status.label}
                      </Badge>
                    </div>
                    <p className="text-sm text-gray-600 line-clamp-2">{project.description}</p>
                  </CardHeader>
                  
                  <CardContent className="space-y-4">
                    {/* 项目统计 */}
                    <div className="flex justify-between text-sm text-gray-600">
                      <div className="flex items-center space-x-1">
                        <FileText className="w-4 h-4" />
                        <span>{(project.total_words || 0).toLocaleString()} 字</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <BookOpen className="w-4 h-4" />
                        <span>{project.estimated_chapters || 0} 章节</span>
                      </div>
                    </div>

                    {/* 时间信息 */}
                    <div className="flex items-center space-x-1 text-xs text-gray-500">
                      <Calendar className="w-3 h-3" />
                      <span>创建于 {formatDate(project.created_at)}</span>
                    </div>

                    {/* 操作按钮 */}
                    <div className="flex space-x-2">
                      <Button
                        variant="default"
                        size="sm"
                        onClick={() => router.push(`${nextStep.path}/${project.id}`)}
                        className="flex-1"
                      >
                        <ArrowRight className="w-3 h-3 mr-1" />
                        {nextStep.label}
                      </Button>
                      
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDeleteProject(project.id)}
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="w-3 h-3" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        )}
      </div>
    </PageContainer>
  );
}
