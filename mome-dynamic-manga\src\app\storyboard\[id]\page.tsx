'use client';

import { useState, useEffect, useCallback } from 'react';
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation';
import { PageContainer } from '@/components/layout/PageContainer';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { StoryboardEditor, EditableStoryboardItem, StoryboardTemplate } from '@/lib/storyboardEditor';
import { ScriptResult } from '@/lib/scriptGenerator';
import { ProjectService, ScriptService, StoryboardService } from '@/lib/databaseService';
import { 
  Film, 
  Play, 
  ArrowRight,
  Loader2,
  CheckCircle,
  Clock,
  Users,
  MapPin,
  Camera,
  Save,
  RefreshCw,
  Eye,
  Settings,
  Clapperboard,
  Plus,
  Trash2,
  Copy,
  Edit3,
  Image as ImageIcon,
  Grid3X3,
  BarChart3,
  Undo,
  Redo,
  Download,
  Upload,
  PlayCircle
} from 'lucide-react';

// 类型定义
interface Project {
  id: string;
  name: string;
  description: string;
  status: string;
}

interface ShotTypeOption {
  id: string;
  name: string;
  description: string;
}

interface CameraAngleOption {
  id: string;
  name: string;
  description: string;
}

export default function StoryboardPage() {
  const params = useParams();
  const router = useRouter();
  const projectId = params.id as string;
  
  const [project, setProject] = useState<Project | null>(null);
  const [script, setScript] = useState<ScriptResult | null>(null);
  const [storyboardEditor, setStoryboardEditor] = useState<StoryboardEditor | null>(null);
  const [editorState, setEditorState] = useState<any>(null);
  const [shotTypes, setShotTypes] = useState<ShotTypeOption[]>([]);
  const [cameraAngles, setCameraAngles] = useState<CameraAngleOption[]>([]);
  const [templates, setTemplates] = useState<StoryboardTemplate[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [selectedItemId, setSelectedItemId] = useState<string | null>(null);
  const [viewMode, setViewMode] = useState<'grid' | 'list' | 'timeline'>('grid');
  const [imageConfig, setImageConfig] = useState({
    style: 'manga' as 'manga' | 'anime' | 'realistic' | 'sketch',
    aspectRatio: '16:9' as '16:9' | '4:3' | '1:1' | '9:16',
    quality: 'high' as 'standard' | 'high',
    provider: 'openrouter' as 'openrouter' | 'google'
  });

  // 批量生成状态
  const [batchGenerating, setBatchGenerating] = useState(false);
  const [batchProgress, setBatchProgress] = useState({ current: 0, total: 0 });
  const [generationQueue, setGenerationQueue] = useState<string[]>([]);

  // API并发限制配置
  const MAX_CONCURRENT_REQUESTS = 3;

  // 图片预览弹窗状态
  const [previewImage, setPreviewImage] = useState<{url: string, title: string} | null>(null);

  useEffect(() => {
    loadData();
    loadConfig();
  }, [projectId]);

  const loadData = async () => {
    try {
      // 从数据库读取项目数据
      const project = await ProjectService.getById(projectId);
      if (project) {
        setProject(project);
      }

      // 从数据库读取剧本数据
      const scripts = await ScriptService.getByProjectId(projectId);
      if (scripts && scripts.length > 0) {
        const script = scripts[0];
        setScript(script);

        // 检查是否已有分镜数据
        const storyboards = await StoryboardService.getByProjectId(projectId);
        let storyboardData = [];

        if (storyboards && storyboards.length > 0) {
          // 使用已保存的分镜数据（从 panels 字段读取）
          storyboardData = storyboards[0].panels || [];
          console.log('✅ 从数据库加载分镜数据:', storyboardData.length, '个项目');
          console.log('🖼️ 分镜数据详情:', storyboardData);

          // 检查图片数据（优先使用imageUrl，其次是imageData）
          const imagesWithUrl = storyboardData.filter((item: any) => item.imageUrl).length;
          const imagesWithData = storyboardData.filter((item: any) => item.imageData).length;
          console.log('🖼️ 图片统计:', {
            总分镜数: storyboardData.length,
            有URL图片: imagesWithUrl,
            有Base64图片: imagesWithData,
            数据库记录的图片数: storyboards[0].total_images || 0
          });

          // 验证图片URL是否可访问
          storyboardData.forEach((item: any, index: number) => {
            if (item.imageUrl) {
              console.log(`🖼️ 分镜 ${index + 1} 图片URL:`, item.imageUrl.substring(0, 100) + '...');
            }
          });
        } else if (script.storyboard && script.storyboard.length > 0) {
          // 使用剧本中的分镜数据
          storyboardData = script.storyboard;
          console.log('✅ 从剧本加载分镜数据:', storyboardData.length, '个项目');
        }

        // 初始化分镜编辑器
        const editor = new StoryboardEditor(storyboardData);
        setStoryboardEditor(editor);

        // 订阅状态变化
        editor.subscribe(setEditorState);
        setEditorState(editor.getState());
      }
    } catch (error) {
      console.error('加载数据失败:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const loadConfig = async () => {
    try {
      const response = await fetch('/api/storyboard?action=templates');
      const result = await response.json();
      
      if (result.success) {
        setTemplates(result.data.templates);
        setShotTypes(result.data.shotTypes);
        setCameraAngles(result.data.cameraAngles);
      }
    } catch (error) {
      console.error('加载配置失败:', error);
    }
  };

  const handleSaveStoryboard = async () => {
    if (!storyboardEditor) return;

    setIsSaving(true);
    try {
      const storyboardItems = storyboardEditor.exportStoryboard();
      
      const response = await fetch('/api/storyboard', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          projectId,
          storyboardItems,
          action: 'save'
        })
      });

      const result = await response.json();

      if (result.success) {
        console.log('✅ 分镜数据已成功保存到Supabase数据库');

        // 重置编辑状态
        storyboardEditor.dispatch({ type: 'RESET_CHANGES' });

        // 显示成功提示
        alert('分镜表保存成功！');

        // 更新项目状态（只更新项目状态，不保存分镜数据到localStorage）
        const projectData = localStorage.getItem(`project_${projectId}`);
        if (projectData) {
          const project = JSON.parse(projectData);
          const updatedProject = { ...project, status: 'storyboard_completed' };
          localStorage.setItem(`project_${projectId}`, JSON.stringify(updatedProject));
        }
      } else {
        throw new Error(result.error || '保存分镜表失败');
      }
    } catch (error) {
      console.error('保存分镜表失败:', error);
      alert(error instanceof Error ? error.message : '保存分镜表失败');
    } finally {
      setIsSaving(false);
    }
  };

  const handleUpdateItem = useCallback((id: string, updates: Partial<EditableStoryboardItem>) => {
    if (!storyboardEditor) return;
    
    storyboardEditor.dispatch({
      type: 'UPDATE_ITEM',
      id,
      updates
    });
  }, [storyboardEditor]);

  const handleAddItem = useCallback((afterId?: string) => {
    if (!storyboardEditor || !editorState) return;
    
    const lastItem = editorState.items[editorState.items.length - 1];
    const newItem = StoryboardEditor.createNewStoryboardItem(
      lastItem?.sceneNumber || 1,
      (lastItem?.shotNumber || 0) + 1
    );
    
    storyboardEditor.dispatch({
      type: 'ADD_ITEM',
      item: newItem,
      afterId
    });
  }, [storyboardEditor, editorState]);

  const handleDeleteItem = useCallback((id: string) => {
    if (!storyboardEditor) return;
    
    if (confirm('确定要删除这个分镜吗？')) {
      storyboardEditor.dispatch({
        type: 'DELETE_ITEM',
        id
      });
    }
  }, [storyboardEditor]);

  const handleDuplicateItem = useCallback((id: string) => {
    if (!storyboardEditor) return;
    
    storyboardEditor.dispatch({
      type: 'DUPLICATE_ITEM',
      id
    });
  }, [storyboardEditor]);

  // 下载图片功能
  const handleDownloadImage = async (item: EditableStoryboardItem) => {
    if (!item.imageUrl) {
      console.warn('没有图片可下载');
      return;
    }

    try {
      console.log('📥 开始下载图片:', item.id);

      let imageData: string;

      if (item.imageUrl.startsWith('data:image/')) {
        // Base64 图片直接使用
        imageData = item.imageUrl;
      } else if (item.imageUrl.startsWith('http')) {
        // 网络图片需要先获取
        const response = await fetch(item.imageUrl);
        const blob = await response.blob();
        imageData = await new Promise<string>((resolve) => {
          const reader = new FileReader();
          reader.onload = () => resolve(reader.result as string);
          reader.readAsDataURL(blob);
        });
      } else {
        throw new Error('不支持的图片格式');
      }

      // 创建下载链接
      const link = document.createElement('a');
      link.href = imageData;
      link.download = `分镜_${item.shotNumber || item.id}_${Date.now()}.png`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      console.log('✅ 图片下载成功');
    } catch (error) {
      console.error('❌ 图片下载失败:', error);
      alert('图片下载失败，请重试');
    }
  };

  // 批量生成功能
  const handleBatchGenerate = async () => {
    if (!editorState?.items) {
      console.warn('没有分镜数据');
      return;
    }

    // 找出所有没有图片的分镜
    const itemsWithoutImages = editorState.items.filter((item: EditableStoryboardItem) =>
      !item.imageUrl || item.imageUrl.trim() === ''
    );

    if (itemsWithoutImages.length === 0) {
      alert('所有分镜都已有图片！');
      return;
    }

    console.log(`🚀 开始批量生成 ${itemsWithoutImages.length} 个分镜的图片`);

    setBatchGenerating(true);
    setBatchProgress({ current: 0, total: itemsWithoutImages.length });
    setGenerationQueue(itemsWithoutImages.map(item => item.id));

    let completed = 0;
    let currentBatch: Promise<void>[] = [];

    // 处理单个分镜生成
    const generateSingleItem = async (item: EditableStoryboardItem): Promise<void> => {
      try {
        console.log(`🎨 生成分镜 ${item.id} (${completed + 1}/${itemsWithoutImages.length})`);

        // 更新状态为生成中
        handleUpdateItem(item.id, { imageStatus: 'generating' });

        // 构建视觉提示词
        const visualPrompt = item.visualPrompt || StoryboardEditor.generateVisualPrompt(item);

        // 调用生成API
        const response = await fetch('/api/generate-image', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            prompt: visualPrompt,
            config: imageConfig,
            itemId: item.id,
            projectId: projectId
          })
        });

        const result = await response.json();

        if (result.success && result.data?.imageUrl) {
          // 更新成功状态
          handleUpdateItem(item.id, {
            imageUrl: result.data.imageUrl,
            imageStatus: 'generated'
          });
          console.log(`✅ 分镜 ${item.id} 生成成功`);
        } else {
          throw new Error(result.error || '生成失败');
        }

      } catch (error) {
        console.error(`❌ 分镜 ${item.id} 生成失败:`, error);
        handleUpdateItem(item.id, { imageStatus: 'error' });
      } finally {
        completed++;
        setBatchProgress({ current: completed, total: itemsWithoutImages.length });

        // 从队列中移除
        setGenerationQueue(prev => prev.filter(id => id !== item.id));
      }
    };

    // 分批处理，控制并发数量
    for (let i = 0; i < itemsWithoutImages.length; i += MAX_CONCURRENT_REQUESTS) {
      const batch = itemsWithoutImages.slice(i, i + MAX_CONCURRENT_REQUESTS);

      console.log(`📦 处理批次 ${Math.floor(i / MAX_CONCURRENT_REQUESTS) + 1}，包含 ${batch.length} 个分镜`);

      // 等待当前批次完成
      currentBatch = batch.map(item => generateSingleItem(item));
      await Promise.allSettled(currentBatch);

      // 如果不是最后一批，稍微延迟避免API限制
      if (i + MAX_CONCURRENT_REQUESTS < itemsWithoutImages.length) {
        console.log('⏳ 等待 2 秒后处理下一批...');
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
    }

    setBatchGenerating(false);
    setBatchProgress({ current: 0, total: 0 });
    setGenerationQueue([]);

    console.log('🎉 批量生成完成！');
    alert(`批量生成完成！成功生成 ${completed} 个分镜图片。`);
  };

  const handleGenerateImage = async (item: EditableStoryboardItem) => {
    try {
      console.log('🎨 开始为分镜项目生成图像:', item.id);

      // 检查是否已经在生成中，避免重复请求
      if (item.imageStatus === 'generating') {
        console.log('⚠️ 图像已在生成中，跳过重复请求:', item.id);
        return;
      }

      // 更新状态为生成中
      handleUpdateItem(item.id, { imageStatus: 'generating' });

      // 构建视觉提示词，优先使用用户自定义的提示词
      const visualPrompt = item.visualPrompt || StoryboardEditor.generateVisualPrompt(item);

      console.log('📝 使用提示词:', visualPrompt.substring(0, 100) + '...');

      // 使用新的 Gemini 图像生成 API
      const response = await fetch('/api/generate-image', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          prompt: visualPrompt,
          config: imageConfig,
          itemId: item.id,
          projectId: projectId
        })
      });

      const result = await response.json();

      if (result.success) {
        console.log('✅ 图像生成成功');

        handleUpdateItem(item.id, {
          imageUrl: result.data.imageUrl,
          imageStatus: 'generated',
          imagePrompt: visualPrompt
        });

        // 显示成功提示
        alert('图像生成成功！');
      } else {
        console.error('❌ 图像生成失败:', result.error);
        throw new Error(result.error || '图像生成失败');
      }
    } catch (error) {
      console.error('❌ 图像生成异常:', error);
      handleUpdateItem(item.id, { imageStatus: 'error' });

      // 显示详细错误信息
      const errorMessage = error instanceof Error ? error.message : '图像生成失败';
      alert(`图像生成失败: ${errorMessage}`);
    }
  };

  const statistics = storyboardEditor?.getStatistics();
  const selectedItem = editorState?.items.find((item: EditableStoryboardItem) => item.id === selectedItemId);

  if (isLoading) {
    return (
      <PageContainer
        title="分镜编辑"
        description="可视化分镜表编辑和图像生成"
        currentStep={5}
        projectId={projectId}
      >
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-black mx-auto mb-4"></div>
            <p className="text-gray-600">正在加载数据...</p>
          </div>
        </div>
      </PageContainer>
    );
  }

  if (!project || !script) {
    return (
      <PageContainer
        title="分镜编辑"
        description="可视化分镜表编辑和图像生成"
        currentStep={5}
        projectId={projectId}
      >
        <div className="text-center py-12">
          <p className="text-gray-600">项目或剧本数据不存在</p>
          <Button onClick={() => router.push('/')} className="mt-4">
            返回首页
          </Button>
        </div>
      </PageContainer>
    );
  }

  if (!storyboardEditor || !editorState) {
    return (
      <PageContainer
        title="分镜编辑"
        description="可视化分镜表编辑和图像生成"
        currentStep={5}
        projectId={projectId}
      >
        <div className="text-center py-12">
          <p className="text-gray-600">分镜编辑器初始化失败</p>
          <Button onClick={() => router.push(`/script/${projectId}`)} className="mt-4">
            返回剧本页面
          </Button>
        </div>
      </PageContainer>
    );
  }

  return (
    <PageContainer
      title="分镜编辑"
      description="可视化分镜表编辑和图像生成"
      currentStep={5}
      projectId={projectId}
      nextStep={{
        title: "完成制作",
        href: `/preview/${projectId}`,
        disabled: !statistics || statistics.hasUnsavedChanges
      }}
    >
      <div className="space-y-6">
        {/* 顶部工具栏 */}
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2">
                  <Clapperboard className="w-5 h-5" />
                  <span className="font-medium">{script.title}</span>
                </div>
                
                {statistics && (
                  <div className="flex items-center space-x-4 text-sm text-gray-600">
                    <span>{statistics.totalShots} 个分镜</span>
                    <span>{statistics.totalDuration}秒</span>
                    <span>{statistics.sceneCount} 个场景</span>
                    {statistics.hasUnsavedChanges && (
                      <Badge variant="outline" className="text-orange-600">
                        有未保存的更改
                      </Badge>
                    )}
                  </div>
                )}
              </div>

              <div className="flex items-center space-x-2">
                {/* 视图模式切换 */}
                <div className="flex items-center border rounded-lg">
                  <Button
                    variant={viewMode === 'grid' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => setViewMode('grid')}
                  >
                    <Grid3X3 className="w-4 h-4" />
                  </Button>
                  <Button
                    variant={viewMode === 'list' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => setViewMode('list')}
                  >
                    <BarChart3 className="w-4 h-4" />
                  </Button>
                </div>

                {/* 操作按钮 */}
                <div className="flex items-center space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleAddItem()}
                  >
                    <Plus className="w-4 h-4 mr-2" />
                    添加分镜
                  </Button>

                  {/* 图像生成配置 */}
                  <Dialog>
                    <DialogTrigger asChild>
                      <Button variant="outline" size="sm">
                        <Settings className="w-4 h-4 mr-2" />
                        图像设置
                      </Button>
                    </DialogTrigger>
                    <DialogContent>
                      <DialogHeader>
                        <DialogTitle>图像生成配置</DialogTitle>
                        <DialogDescription>
                          配置 AI 图像生成的风格和参数
                        </DialogDescription>
                      </DialogHeader>
                      <div className="space-y-4">
                        <div>
                          <label className="text-sm font-medium">风格</label>
                          <Select
                            value={imageConfig.style}
                            onValueChange={(value: any) => setImageConfig(prev => ({ ...prev, style: value }))}
                          >
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="manga">🎌 漫画风格</SelectItem>
                              <SelectItem value="anime">🎨 动画风格</SelectItem>
                              <SelectItem value="realistic">📷 写实风格</SelectItem>
                              <SelectItem value="sketch">✏️ 素描风格</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>

                        <div>
                          <label className="text-sm font-medium">宽高比</label>
                          <Select
                            value={imageConfig.aspectRatio}
                            onValueChange={(value: any) => setImageConfig(prev => ({ ...prev, aspectRatio: value }))}
                          >
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="16:9">16:9 宽屏</SelectItem>
                              <SelectItem value="4:3">4:3 标准</SelectItem>
                              <SelectItem value="1:1">1:1 正方形</SelectItem>
                              <SelectItem value="9:16">9:16 竖屏</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>

                        <div>
                          <label className="text-sm font-medium">质量</label>
                          <Select
                            value={imageConfig.quality}
                            onValueChange={(value: any) => setImageConfig(prev => ({ ...prev, quality: value }))}
                          >
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="standard">标准质量</SelectItem>
                              <SelectItem value="high">高质量</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>

                        <div>
                          <label className="text-sm font-medium">API 提供商</label>
                          <Select
                            value={imageConfig.provider}
                            onValueChange={(value: any) => setImageConfig(prev => ({ ...prev, provider: value }))}
                          >
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="openrouter">🔄 OpenRouter (推荐)</SelectItem>
                              <SelectItem value="google">🔗 Google 直连</SelectItem>
                            </SelectContent>
                          </Select>
                          <p className="text-xs text-gray-500 mt-1">
                            OpenRouter 通过代理访问，避免配额限制
                          </p>
                        </div>
                      </div>
                    </DialogContent>
                  </Dialog>
                </div>

                {/* 批量生成按钮 */}
                <Button
                  onClick={handleBatchGenerate}
                  disabled={batchGenerating || !editorState?.items?.some((item: EditableStoryboardItem) => !item.imageUrl)}
                  size="sm"
                  variant="outline"
                >
                  {batchGenerating ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      生成中 {batchProgress.current}/{batchProgress.total}
                    </>
                  ) : (
                    <>
                      <PlayCircle className="w-4 h-4 mr-2" />
                      一键生成图片
                    </>
                  )}
                </Button>

                <Button
                  onClick={handleSaveStoryboard}
                  disabled={isSaving || !statistics?.hasUnsavedChanges}
                  size="sm"
                >
                  {isSaving ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      保存中...
                    </>
                  ) : (
                    <>
                      <Save className="w-4 h-4 mr-2" />
                      保存分镜表
                    </>
                  )}
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 分镜列表 */}
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* 左侧：分镜列表 */}
          <div className="lg:col-span-3">
            {viewMode === 'grid' ? (
              <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4">
                {editorState.items.map((item: EditableStoryboardItem, index: number) => (
                  <StoryboardItemCard
                    key={item.id}
                    item={item}
                    index={index}
                    isSelected={selectedItemId === item.id}
                    onSelect={() => setSelectedItemId(item.id)}
                    onUpdate={handleUpdateItem}
                    onDelete={handleDeleteItem}
                    onDuplicate={handleDuplicateItem}
                    onGenerateImage={handleGenerateImage}
                    onDownloadImage={handleDownloadImage}
                    onPreviewImage={setPreviewImage}
                  />
                ))}
              </div>
            ) : (
              <div className="space-y-4">
                {editorState.items.map((item: EditableStoryboardItem, index: number) => (
                  <StoryboardItemRow
                    key={item.id}
                    item={item}
                    index={index}
                    isSelected={selectedItemId === item.id}
                    onSelect={() => setSelectedItemId(item.id)}
                    onUpdate={handleUpdateItem}
                    onDelete={handleDeleteItem}
                    onDuplicate={handleDuplicateItem}
                    onGenerateImage={handleGenerateImage}
                    onDownloadImage={handleDownloadImage}
                  />
                ))}
              </div>
            )}
          </div>

          {/* 右侧：编辑面板 */}
          <div className="lg:col-span-1">
            {selectedItem ? (
              <StoryboardEditPanel
                item={selectedItem}
                shotTypes={shotTypes}
                cameraAngles={cameraAngles}
                onUpdate={handleUpdateItem}
                onGenerateImage={handleGenerateImage}
                onDownloadImage={handleDownloadImage}
                onPreviewImage={setPreviewImage}
              />
            ) : (
              <Card>
                <CardContent className="p-6 text-center">
                  <Camera className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    选择分镜进行编辑
                  </h3>
                  <p className="text-gray-600 mb-4">
                    点击左侧的分镜卡片来查看和编辑详细信息
                  </p>
                  <Button onClick={() => handleAddItem()} variant="outline">
                    <Plus className="w-4 h-4 mr-2" />
                    添加新分镜
                  </Button>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>

      {/* 图片预览弹窗 */}
      {previewImage && (
        <Dialog open={!!previewImage} onOpenChange={() => setPreviewImage(null)}>
          <DialogContent className="max-w-4xl max-h-[90vh] p-0">
            <DialogHeader className="p-6 pb-2">
              <DialogTitle className="text-lg font-medium text-gray-900">
                {previewImage.title}
              </DialogTitle>
            </DialogHeader>
            <div className="px-6 pb-6">
              <div className="relative bg-gray-50 rounded-lg overflow-hidden">
                <img
                  src={previewImage.url}
                  alt={previewImage.title}
                  className="w-full h-auto max-h-[70vh] object-contain"
                />
              </div>
              <div className="flex justify-end mt-4 space-x-2">
                <Button
                  variant="outline"
                  onClick={() => {
                    const link = document.createElement('a');
                    link.href = previewImage.url;
                    link.download = `${previewImage.title}.png`;
                    link.click();
                  }}
                >
                  <Download className="w-4 h-4 mr-2" />
                  下载图片
                </Button>
                <Button onClick={() => setPreviewImage(null)}>
                  关闭
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      )}
    </PageContainer>
  );
}

// 分镜卡片组件
function StoryboardItemCard({
  item,
  index,
  isSelected,
  onSelect,
  onUpdate,
  onDelete,
  onDuplicate,
  onGenerateImage,
  onDownloadImage,
  onPreviewImage
}: {
  item: EditableStoryboardItem;
  index: number;
  isSelected: boolean;
  onSelect: () => void;
  onUpdate: (id: string, updates: Partial<EditableStoryboardItem>) => void;
  onDelete: (id: string) => void;
  onDuplicate: (id: string) => void;
  onGenerateImage: (item: EditableStoryboardItem) => void;
  onDownloadImage: (item: EditableStoryboardItem) => void;
  onPreviewImage: (preview: {url: string, title: string}) => void;
}) {
  return (
    <Card
      className={`cursor-pointer transition-all ${
        isSelected ? 'ring-2 ring-blue-500 shadow-lg' : 'hover:shadow-md'
      } ${item.hasChanges ? 'border-orange-300' : ''}`}
      onClick={onSelect}
    >
      <CardHeader className="p-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Badge variant="outline" className="text-xs">
              场景 {item.sceneNumber}
            </Badge>
            <Badge variant="secondary" className="text-xs">
              镜头 {item.shotNumber}
            </Badge>
          </div>
          <div className="flex items-center space-x-1">
            {item.hasChanges && (
              <div className="w-2 h-2 bg-orange-400 rounded-full" title="有未保存的更改" />
            )}
            <Button
              variant="ghost"
              size="sm"
              onClick={(e) => {
                e.stopPropagation();
                onDuplicate(item.id);
              }}
            >
              <Copy className="w-3 h-3" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={(e) => {
                e.stopPropagation();
                onDelete(item.id);
              }}
            >
              <Trash2 className="w-3 h-3" />
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent className="p-3 pt-0">
        {/* 图像预览区域 */}
        <div className="aspect-video bg-gray-100 rounded-lg mb-3 flex items-center justify-center relative overflow-hidden group">
          {item.imageUrl ? (
            <>
              <img
                src={item.imageUrl}
                alt={`分镜 ${item.shotNumber}`}
                className="w-full h-full object-cover cursor-pointer hover:opacity-90 transition-opacity"
                onClick={() => onPreviewImage({
                  url: item.imageUrl!,
                  title: `分镜 ${item.sceneNumber}-${item.shotNumber}`
                })}
              />
              {/* 下载按钮 - 悬停时显示 */}
              <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                <Button
                  size="sm"
                  variant="secondary"
                  onClick={(e) => {
                    e.stopPropagation();
                    onDownloadImage(item);
                  }}
                  className="bg-white/90 hover:bg-white text-gray-700 shadow-sm"
                >
                  <Download className="w-3 h-3" />
                </Button>
              </div>
            </>
          ) : (
            <div className="text-center">
              <ImageIcon className="w-8 h-8 text-gray-400 mx-auto mb-2" />
              <Button
                size="sm"
                variant="outline"
                onClick={(e) => {
                  e.stopPropagation();
                  onGenerateImage(item);
                }}
                disabled={item.imageStatus === 'generating'}
              >
                {item.imageStatus === 'generating' ? (
                  <>
                    <Loader2 className="w-3 h-3 mr-1 animate-spin" />
                    生成中...
                  </>
                ) : (
                  <>
                    <ImageIcon className="w-3 h-3 mr-1" />
                    生成图像
                  </>
                )}
              </Button>
            </div>
          )}

          {item.imageStatus === 'generating' && (
            <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
              <div className="text-white text-center">
                <Loader2 className="w-6 h-6 animate-spin mx-auto mb-2" />
                <p className="text-sm">AI生成中...</p>
              </div>
            </div>
          )}
        </div>

        {/* 分镜信息 */}
        <div className="space-y-2">
          <div className="flex items-center space-x-2 text-xs text-gray-600">
            <Badge variant="outline" className="text-xs">
              {item.shotType}
            </Badge>
            <Badge variant="outline" className="text-xs">
              {item.cameraAngle}
            </Badge>
            <span>{item.duration}秒</span>
          </div>

          <div className="text-sm">
            <p className="font-medium text-gray-900 line-clamp-1">{item.location}</p>
            <p className="text-gray-600 line-clamp-2 text-xs mt-1">{item.action}</p>
          </div>

          {item.dialogue && (
            <div className="text-xs text-gray-600 bg-gray-50 p-2 rounded">
              <p className="line-clamp-2">"{item.dialogue}"</p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

// 分镜行组件（列表视图）
function StoryboardItemRow({
  item,
  index,
  isSelected,
  onSelect,
  onUpdate,
  onDelete,
  onDuplicate,
  onGenerateImage,
  onDownloadImage
}: {
  item: EditableStoryboardItem;
  index: number;
  isSelected: boolean;
  onSelect: () => void;
  onUpdate: (id: string, updates: Partial<EditableStoryboardItem>) => void;
  onDelete: (id: string) => void;
  onDuplicate: (id: string) => void;
  onGenerateImage: (item: EditableStoryboardItem) => void;
  onDownloadImage: (item: EditableStoryboardItem) => void;
}) {
  return (
    <Card
      className={`cursor-pointer transition-all ${
        isSelected ? 'ring-2 ring-blue-500 shadow-lg' : 'hover:shadow-md'
      } ${item.hasChanges ? 'border-orange-300' : ''}`}
      onClick={onSelect}
    >
      <CardContent className="p-4">
        <div className="flex items-center space-x-4">
          {/* 缩略图 */}
          <div className="w-24 h-16 bg-gray-100 rounded flex items-center justify-center flex-shrink-0">
            {item.imageUrl ? (
              <img
                src={item.imageUrl}
                alt={`分镜 ${item.shotNumber}`}
                className="w-full h-full object-cover rounded"
              />
            ) : (
              <ImageIcon className="w-6 h-6 text-gray-400" />
            )}
          </div>

          {/* 分镜信息 */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center space-x-2 mb-2">
              <Badge variant="outline" className="text-xs">
                场景 {item.sceneNumber} - 镜头 {item.shotNumber}
              </Badge>
              <Badge variant="secondary" className="text-xs">
                {item.shotType}
              </Badge>
              <Badge variant="secondary" className="text-xs">
                {item.cameraAngle}
              </Badge>
              <span className="text-xs text-gray-600">{item.duration}秒</span>
              {item.hasChanges && (
                <Badge variant="outline" className="text-xs text-orange-600">
                  已修改
                </Badge>
              )}
            </div>

            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <p className="font-medium text-gray-900">{item.location}</p>
                <p className="text-gray-600 line-clamp-1">{item.action}</p>
              </div>
              {item.dialogue && (
                <div>
                  <p className="text-gray-600 line-clamp-1">"{item.dialogue}"</p>
                </div>
              )}
            </div>
          </div>

          {/* 操作按钮 */}
          <div className="flex items-center space-x-2 flex-shrink-0">
            <Button
              size="sm"
              variant="outline"
              onClick={(e) => {
                e.stopPropagation();
                onGenerateImage(item);
              }}
              disabled={item.imageStatus === 'generating'}
            >
              {item.imageStatus === 'generating' ? (
                <Loader2 className="w-4 h-4 animate-spin" />
              ) : (
                <ImageIcon className="w-4 h-4" />
              )}
            </Button>
            <Button
              size="sm"
              variant="ghost"
              onClick={(e) => {
                e.stopPropagation();
                onDuplicate(item.id);
              }}
            >
              <Copy className="w-4 h-4" />
            </Button>
            <Button
              size="sm"
              variant="ghost"
              onClick={(e) => {
                e.stopPropagation();
                onDelete(item.id);
              }}
            >
              <Trash2 className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

// 分镜编辑面板组件
function StoryboardEditPanel({
  item,
  shotTypes,
  cameraAngles,
  onUpdate,
  onGenerateImage,
  onDownloadImage,
  onPreviewImage
}: {
  item: EditableStoryboardItem;
  shotTypes: ShotTypeOption[];
  cameraAngles: CameraAngleOption[];
  onUpdate: (id: string, updates: Partial<EditableStoryboardItem>) => void;
  onGenerateImage: (item: EditableStoryboardItem) => void;
  onDownloadImage: (item: EditableStoryboardItem) => void;
  onPreviewImage: (preview: {url: string, title: string}) => void;
}) {
  const handleFieldUpdate = (field: keyof EditableStoryboardItem, value: any) => {
    onUpdate(item.id, { [field]: value });
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Edit3 className="w-5 h-5" />
          <span>编辑分镜</span>
          <Badge variant="outline" className="text-xs">
            场景 {item.sceneNumber} - 镜头 {item.shotNumber}
          </Badge>
        </CardTitle>
      </CardHeader>

      <CardContent className="space-y-6 max-h-[calc(100vh-200px)] overflow-y-auto">
        {/* 基本信息 */}
        <div className="space-y-4">
          <div className="flex items-center gap-2 pb-2 border-b border-gray-100">
            <div className="w-1 h-4 bg-black"></div>
            <h4 className="text-sm font-medium text-gray-900">基本信息</h4>
          </div>

          <div className="space-y-4">
            {/* 第一行：场景号 + 镜头号 */}
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-1.5">
                <label className="text-xs font-medium text-gray-700 block">场景号</label>
                <Input
                  type="number"
                  value={item.sceneNumber}
                  onChange={(e) => handleFieldUpdate('sceneNumber', parseInt(e.target.value) || 1)}
                  min={1}
                  className="h-9 text-sm font-medium text-gray-900 border-gray-300 focus:border-black focus:ring-1 focus:ring-black"
                />
              </div>
              <div className="space-y-1.5">
                <label className="text-xs font-medium text-gray-700 block">镜头号</label>
                <Input
                  type="number"
                  value={item.shotNumber}
                  onChange={(e) => handleFieldUpdate('shotNumber', parseInt(e.target.value) || 1)}
                  min={1}
                  className="h-9 text-sm font-medium text-gray-900 border-gray-300 focus:border-black focus:ring-1 focus:ring-black"
                />
              </div>
            </div>

            {/* 第二行：时长 + 情绪 */}
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-1.5">
                <label className="text-xs font-medium text-gray-700 block">时长（秒）</label>
                <Input
                  type="number"
                  value={item.duration}
                  onChange={(e) => handleFieldUpdate('duration', parseInt(e.target.value) || 1)}
                  min={1}
                  max={60}
                  className="h-9 text-sm font-medium text-gray-900 border-gray-300 focus:border-black focus:ring-1 focus:ring-black"
                />
              </div>
              <div className="space-y-1.5">
                <label className="text-xs font-medium text-gray-700 block">情绪</label>
                <Input
                  value={item.emotion}
                  onChange={(e) => handleFieldUpdate('emotion', e.target.value)}
                  placeholder="情绪氛围"
                  className="h-9 text-sm font-medium text-gray-900 border-gray-300 focus:border-black focus:ring-1 focus:ring-black"
                />
              </div>
            </div>
          </div>
        </div>

        {/* 镜头参数 */}
        <div className="space-y-4">
          <div className="flex items-center gap-2 pb-2 border-b border-gray-100">
            <div className="w-1 h-4 bg-black"></div>
            <h4 className="text-sm font-medium text-gray-900">镜头参数</h4>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-1.5">
              <label className="text-xs font-medium text-gray-700 block">镜头类型</label>
              <Select
                value={item.shotType}
                onValueChange={(value) => handleFieldUpdate('shotType', value)}
              >
                <SelectTrigger className="h-9 text-sm font-medium text-gray-900 border-gray-300 focus:border-black focus:ring-1 focus:ring-black">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {shotTypes.map((type) => (
                    <SelectItem key={type.id} value={type.id}>
                      <div>
                        <div className="font-medium text-sm">{type.name}</div>
                        <div className="text-xs text-gray-500">{type.description}</div>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-1.5">
              <label className="text-xs font-medium text-gray-700 block">镜头角度</label>
              <Select
                value={item.cameraAngle}
                onValueChange={(value) => handleFieldUpdate('cameraAngle', value)}
              >
                <SelectTrigger className="h-9 text-sm font-medium text-gray-900 border-gray-300 focus:border-black focus:ring-1 focus:ring-black">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {cameraAngles.map((angle) => (
                    <SelectItem key={angle.id} value={angle.id}>
                      <div>
                        <div className="font-medium text-sm">{angle.name}</div>
                        <div className="text-xs text-gray-500">{angle.description}</div>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        {/* 场景内容 */}
        <div className="space-y-4">
          <div className="flex items-center gap-2 pb-2 border-b border-gray-100">
            <div className="w-1 h-4 bg-black"></div>
            <h4 className="text-sm font-medium text-gray-900">场景内容</h4>
          </div>

          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-1.5">
                <label className="text-xs font-medium text-gray-700 block">地点</label>
                <Input
                  value={item.location}
                  onChange={(e) => handleFieldUpdate('location', e.target.value)}
                  placeholder="场景地点"
                  className="h-9 text-sm font-medium text-gray-900 border-gray-300 focus:border-black focus:ring-1 focus:ring-black"
                />
              </div>
              <div className="space-y-1.5">
                <label className="text-xs font-medium text-gray-700 block">角色</label>
                <Input
                  value={item.characters.join(', ')}
                  onChange={(e) => handleFieldUpdate('characters', e.target.value.split(',').map(s => s.trim()).filter(Boolean))}
                  placeholder="角色名称，用逗号分隔"
                  className="h-9 text-sm font-medium text-gray-900 border-gray-300 focus:border-black focus:ring-1 focus:ring-black"
                />
              </div>
            </div>

            <div className="space-y-1.5">
              <label className="text-xs font-medium text-gray-700 block">动作描述</label>
              <Textarea
                value={item.action}
                onChange={(e) => handleFieldUpdate('action', e.target.value)}
                placeholder="描述这个镜头中发生的动作"
                rows={2}
                className="text-sm font-medium text-gray-900 resize-none border-gray-300 focus:border-black focus:ring-1 focus:ring-black"
              />
            </div>

            <div className="space-y-1.5">
              <label className="text-xs font-medium text-gray-700 block">对话</label>
              <Textarea
                value={item.dialogue}
                onChange={(e) => handleFieldUpdate('dialogue', e.target.value)}
                placeholder="角色对话内容"
                rows={2}
                className="text-sm font-medium text-gray-900 resize-none border-gray-300 focus:border-black focus:ring-1 focus:ring-black"
              />
            </div>
          </div>
        </div>

        {/* AI 设置 */}
        <div className="space-y-4">
          <div className="flex items-center gap-2 pb-2 border-b border-gray-100">
            <div className="w-1 h-4 bg-black"></div>
            <h4 className="text-sm font-medium text-gray-900">AI 设置</h4>
          </div>

          <div className="space-y-4">
            <div className="space-y-1.5">
              <label className="text-xs font-medium text-gray-700 block">视觉提示词</label>
              <Textarea
                value={item.visualPrompt || StoryboardEditor.generateVisualPrompt(item)}
                onChange={(e) => handleFieldUpdate('visualPrompt', e.target.value)}
                placeholder="AI图像生成的提示词"
                rows={3}
                className="text-sm font-medium text-gray-900 resize-none border-gray-300 focus:border-black focus:ring-1 focus:ring-black"
              />
            </div>

            <div className="space-y-1.5">
              <label className="text-xs font-medium text-gray-700 block">备注</label>
              <Textarea
                value={item.notes}
                onChange={(e) => handleFieldUpdate('notes', e.target.value)}
                placeholder="其他备注信息"
                rows={2}
                className="text-sm font-medium text-gray-900 resize-none border-gray-300 focus:border-black focus:ring-1 focus:ring-black"
              />
            </div>
          </div>
        </div>

        {/* 图像预览 */}
        <div className="space-y-4">
          <div className="flex items-center justify-between pb-2 border-b border-gray-100">
            <div className="flex items-center gap-2">
              <div className="w-1 h-4 bg-black"></div>
              <h4 className="text-sm font-medium text-gray-900">图像预览</h4>
            </div>
            <div className="flex items-center gap-2">
              <Button
                size="sm"
                onClick={() => onGenerateImage(item)}
                disabled={item.imageStatus === 'generating'}
                className="h-7 px-3 text-xs bg-black hover:bg-gray-800 text-white border-0"
              >
                {item.imageStatus === 'generating' ? (
                  <>
                    <Loader2 className="w-3 h-3 mr-1.5 animate-spin" />
                    生成中
                  </>
                ) : (
                  <>
                    <ImageIcon className="w-3 h-3 mr-1.5" />
                    {item.imageUrl ? '重新生成' : '生成图像'}
                  </>
                )}
              </Button>

              {item.imageUrl && (
                <Button
                  size="sm"
                  variant="outline"
                  onClick={(e) => {
                    e.stopPropagation();
                    onDownloadImage(item);
                  }}
                  className="h-7 px-3 text-xs border-gray-200 hover:border-gray-400 hover:bg-gray-50"
                >
                  <Download className="w-3 h-3 mr-1.5" />
                  下载
                </Button>
              )}
            </div>
          </div>

          <div className="aspect-video bg-gray-50 border border-gray-200 rounded-lg flex items-center justify-center overflow-hidden">
            {item.imageUrl ? (
              <img
                src={item.imageUrl}
                alt={`分镜 ${item.shotNumber}`}
                className="w-full h-full object-cover cursor-pointer hover:opacity-90 transition-opacity"
                onClick={() => onPreviewImage({
                  url: item.imageUrl!,
                  title: `分镜 ${item.sceneNumber}-${item.shotNumber}`
                })}
              />
            ) : (
              <div className="text-center text-gray-400">
                <ImageIcon className="w-8 h-8 mx-auto mb-2" />
                <p className="text-xs">点击生成图像</p>
              </div>
            )}
          </div>

          {item.imageStatus === 'error' && (
            <p className="text-xs text-red-500 mt-2">图像生成失败，请重试</p>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
