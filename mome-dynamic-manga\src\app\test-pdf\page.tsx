'use client';

import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { FileParser } from '@/lib/fileParser';

import { FileText, Upload, AlertCircle, CheckCircle } from 'lucide-react';

export default function TestPdfPage() {
  const [testResults, setTestResults] = useState<{
    fileTest?: any;
    libraryTest?: any;
  }>({});

  const testPdfLibrary = async () => {
    console.log('🧪 开始测试PDF.js库...');
    
    try {
      // 测试PDF.js导入
      const pdfjsLib = await import('pdfjs-dist');
      console.log('✅ PDF.js导入成功，版本:', pdfjsLib.version);
      
      // 测试worker配置
      if (typeof window !== 'undefined') {
        const workerSrc = `https://cdnjs.cloudflare.com/ajax/libs/pdf.js/4.0.379/pdf.worker.min.js`;
        pdfjsLib.GlobalWorkerOptions.workerSrc = workerSrc;
        console.log('✅ Worker配置成功');
      }
      
      setTestResults(prev => ({
        ...prev,
        libraryTest: {
          success: true,
          version: pdfjsLib.version,
          message: 'PDF.js库测试成功'
        }
      }));
      
    } catch (error) {
      console.error('❌ PDF.js库测试失败:', error);
      setTestResults(prev => ({
        ...prev,
        libraryTest: {
          success: false,
          error: error instanceof Error ? error.message : '未知错误',
          message: 'PDF.js库测试失败'
        }
      }));
    }
  };

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    console.log('📁 选择的文件:', file.name);
    
    try {
      setTestResults(prev => ({
        ...prev,
        fileTest: { loading: true, message: '正在解析文件...' }
      }));

      const result = await FileParser.parseFile(file);
      
      setTestResults(prev => ({
        ...prev,
        fileTest: {
          success: result.success,
          filename: result.filename,
          content: result.content,
          wordCount: result.wordCount,
          error: result.error,
          message: result.success ? '文件解析成功' : '文件解析失败'
        }
      }));
      
    } catch (error) {
      console.error('❌ 文件解析错误:', error);
      setTestResults(prev => ({
        ...prev,
        fileTest: {
          success: false,
          error: error instanceof Error ? error.message : '未知错误',
          message: '文件解析失败'
        }
      }));
    }
  };

  return (
    <div className="container mx-auto px-4 py-8 max-w-6xl">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">PDF解析功能测试</h1>
        <p className="text-gray-600">
          这个页面用于测试和调试PDF文件解析功能
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* PDF.js库测试 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <FileText className="w-5 h-5" />
              <span>PDF.js库测试</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <Button onClick={testPdfLibrary} className="w-full">
              测试PDF.js库
            </Button>
            
            {testResults.libraryTest && (
              <div className={`p-4 rounded-lg border ${
                testResults.libraryTest.success 
                  ? 'bg-green-50 border-green-200' 
                  : 'bg-red-50 border-red-200'
              }`}>
                <div className="flex items-center space-x-2 mb-2">
                  {testResults.libraryTest.success ? (
                    <CheckCircle className="w-5 h-5 text-green-600" />
                  ) : (
                    <AlertCircle className="w-5 h-5 text-red-600" />
                  )}
                  <span className={`font-medium ${
                    testResults.libraryTest.success ? 'text-green-800' : 'text-red-800'
                  }`}>
                    {testResults.libraryTest.message}
                  </span>
                </div>
                
                {testResults.libraryTest.success && (
                  <div className="text-sm text-green-700">
                    <p>版本: {testResults.libraryTest.version}</p>
                  </div>
                )}
                
                {testResults.libraryTest.error && (
                  <div className="text-sm text-red-700">
                    <p>错误: {testResults.libraryTest.error}</p>
                  </div>
                )}
              </div>
            )}
          </CardContent>
        </Card>

        {/* 文件上传测试 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Upload className="w-5 h-5" />
              <span>文件解析测试</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                选择PDF文件进行测试
              </label>
              <input
                type="file"
                accept=".pdf,.txt,.docx"
                onChange={handleFileUpload}
                className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
              />
            </div>
            
            {testResults.fileTest && (
              <div className={`p-4 rounded-lg border ${
                testResults.fileTest.loading
                  ? 'bg-blue-50 border-blue-200'
                  : testResults.fileTest.success 
                    ? 'bg-green-50 border-green-200' 
                    : 'bg-red-50 border-red-200'
              }`}>
                <div className="flex items-center space-x-2 mb-2">
                  {testResults.fileTest.loading ? (
                    <div className="w-5 h-5 border-2 border-blue-600 border-t-transparent rounded-full animate-spin" />
                  ) : testResults.fileTest.success ? (
                    <CheckCircle className="w-5 h-5 text-green-600" />
                  ) : (
                    <AlertCircle className="w-5 h-5 text-red-600" />
                  )}
                  <span className={`font-medium ${
                    testResults.fileTest.loading
                      ? 'text-blue-800'
                      : testResults.fileTest.success 
                        ? 'text-green-800' 
                        : 'text-red-800'
                  }`}>
                    {testResults.fileTest.message}
                  </span>
                </div>
                
                {testResults.fileTest.success && (
                  <div className="text-sm text-green-700 space-y-1">
                    <p>文件名: {testResults.fileTest.filename}</p>
                    <p>字数: {testResults.fileTest.wordCount}</p>
                    <div>
                      <p>内容预览:</p>
                      <div className="mt-1 p-2 bg-white border rounded text-xs max-h-32 overflow-y-auto">
                        {testResults.fileTest.content?.substring(0, 500)}
                        {testResults.fileTest.content?.length > 500 && '...'}
                      </div>
                    </div>
                  </div>
                )}
                
                {testResults.fileTest.error && (
                  <div className="text-sm text-red-700">
                    <p>错误: {testResults.fileTest.error}</p>
                  </div>
                )}
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* 内置PDF测试组件 */}
      <div className="mt-8">
        <Card>
          <CardHeader>
            <CardTitle>内置PDF测试组件</CardTitle>
          </CardHeader>
          <CardContent>
            <PdfTestComponent />
          </CardContent>
        </Card>
      </div>

      {/* 调试信息 */}
      <div className="mt-8">
        <Card>
          <CardHeader>
            <CardTitle>调试信息</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-sm text-gray-600 space-y-2">
              <p>• 打开浏览器开发者工具查看详细日志</p>
              <p>• PDF.js版本: 4.0.379</p>
              <p>• 支持的文件格式: PDF, TXT, DOCX</p>
              <p>• 最大文件大小: 20MB</p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
