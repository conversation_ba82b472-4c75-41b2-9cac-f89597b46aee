'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { StoryDesignService } from '@/lib/databaseService';

export default function TestStoryDesignPage() {
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState<any>(null);

  const testStoryDesignSave = async () => {
    setIsLoading(true);
    setResult(null);

    try {
      console.log('🧪 开始测试剧情设计保存...');

      const testData = {
        id: `test_story_design_${Date.now()}`,
        project_id: 'test_project_123',
        chapter_id: undefined,
        plot_summary: '测试剧情摘要',
        core_conflict: '测试核心冲突',
        character_analysis: '测试角色分析',
        emotional_arc: '测试情感弧线',
        visual_style: '测试视觉风格',
        key_scenes: [
          { scene: '开场', description: '测试开场描述' },
          { scene: '高潮', description: '测试高潮描述' }
        ],
        adaptation_notes: '测试改编说明',
        ai_model: 'test-model'
      };

      console.log('📝 测试数据:', testData);

      const result = await StoryDesignService.create(testData);
      
      console.log('✅ 保存成功:', result);
      setResult({
        success: true,
        data: result,
        message: '剧情设计保存测试成功！'
      });

    } catch (error) {
      console.error('❌ 测试失败:', error);
      setResult({
        success: false,
        error: error instanceof Error ? error.message : String(error),
        message: '剧情设计保存测试失败'
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <div className="mb-6">
        <h1 className="text-3xl font-bold">剧情设计保存测试</h1>
        <p className="text-gray-600 mt-2">
          测试第三步剧情设计的数据库保存功能
        </p>
      </div>

      <Card className="mb-6">
        <CardHeader>
          <CardTitle>测试操作</CardTitle>
        </CardHeader>
        <CardContent>
          <Button 
            onClick={testStoryDesignSave} 
            disabled={isLoading}
            className="w-full"
          >
            {isLoading ? '测试中...' : '测试剧情设计保存'}
          </Button>
        </CardContent>
      </Card>

      {result && (
        <Card>
          <CardHeader>
            <CardTitle className={result.success ? 'text-green-600' : 'text-red-600'}>
              测试结果
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <p className="font-semibold">{result.message}</p>
              
              {result.success && result.data && (
                <div>
                  <h4 className="font-semibold mb-2">保存的数据:</h4>
                  <pre className="bg-gray-100 p-3 rounded text-sm overflow-auto">
                    {JSON.stringify(result.data, null, 2)}
                  </pre>
                </div>
              )}
              
              {!result.success && result.error && (
                <div>
                  <h4 className="font-semibold mb-2 text-red-600">错误信息:</h4>
                  <div className="bg-red-50 border border-red-200 p-3 rounded text-sm">
                    {result.error}
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      <Card className="mt-6">
        <CardHeader>
          <CardTitle>修复说明</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <p>如果测试失败，请按以下步骤修复：</p>
            <ol className="list-decimal list-inside space-y-2">
              <li>在Supabase控制台执行 <code>database/fix_story_designs.sql</code></li>
              <li>确保 story_designs 表结构正确</li>
              <li>检查RLS策略是否正确设置</li>
              <li>重新测试保存功能</li>
            </ol>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
