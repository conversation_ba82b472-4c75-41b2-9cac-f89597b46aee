'use client';

import { useCallback, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { 
  Upload, 
  File, 
  FileText, 
  X, 
  CheckCircle, 
  AlertCircle,
  Loader2 
} from 'lucide-react';
import { FileParser } from '@/lib/fileParser';
import { FileUploadResult } from '@/types';

interface FileUploaderProps {
  onFileProcessed: (result: FileUploadResult, originalFile: File) => void;
  onFileRemoved?: () => void;
  maxSize?: number; // in bytes
  acceptedTypes?: string[];
  disabled?: boolean;
}

export function FileUploader({
  onFileProcessed,
  onFileRemoved,
  maxSize = 20 * 1024 * 1024, // 20MB
  acceptedTypes = ['.txt', '.docx', '.pdf'],
  disabled = false
}: FileUploaderProps) {
  const [uploadState, setUploadState] = useState<{
    status: 'idle' | 'processing' | 'success' | 'error';
    file: File | null;
    result: FileUploadResult | null;
    progress: number;
    error: string | null;
  }>({
    status: 'idle',
    file: null,
    result: null,
    progress: 0,
    error: null
  });

  const processFile = useCallback(async (file: File) => {
    setUploadState(prev => ({
      ...prev,
      status: 'processing',
      file,
      progress: 0,
      error: null
    }));

    try {
      // 模拟进度更新
      const progressInterval = setInterval(() => {
        setUploadState(prev => ({
          ...prev,
          progress: Math.min(prev.progress + 10, 90)
        }));
      }, 200);

      // 解析文件
      const result = await FileParser.parseFile(file);

      clearInterval(progressInterval);

      if (result.success) {
        setUploadState(prev => ({
          ...prev,
          status: 'success',
          result,
          progress: 100
        }));
        onFileProcessed(result, file);
      } else {
        setUploadState(prev => ({
          ...prev,
          status: 'error',
          error: result.error || '文件处理失败',
          progress: 0
        }));
      }
    } catch (error) {
      setUploadState(prev => ({
        ...prev,
        status: 'error',
        error: error instanceof Error ? error.message : '文件处理失败',
        progress: 0
      }));
    }
  }, [onFileProcessed]);

  const onDrop = useCallback((acceptedFiles: File[], rejectedFiles: any[]) => {
    if (rejectedFiles.length > 0) {
      const rejection = rejectedFiles[0];
      let errorMessage = '文件上传失败';
      
      if (rejection.errors.some((e: any) => e.code === 'file-too-large')) {
        errorMessage = `文件大小超过限制（最大 ${Math.round(maxSize / 1024 / 1024)}MB）`;
      } else if (rejection.errors.some((e: any) => e.code === 'file-invalid-type')) {
        errorMessage = `不支持的文件格式，请上传 ${acceptedTypes.join(', ')} 文件`;
      }

      setUploadState(prev => ({
        ...prev,
        status: 'error',
        error: errorMessage
      }));
      return;
    }

    if (acceptedFiles.length > 0) {
      const file = acceptedFiles[0];
      const validation = FileParser.validateFile(file);
      
      if (!validation.valid) {
        setUploadState(prev => ({
          ...prev,
          status: 'error',
          error: validation.error || '文件验证失败'
        }));
        return;
      }

      processFile(file);
    }
  }, [maxSize, acceptedTypes, processFile]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'text/plain': ['.txt'],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
      'application/pdf': ['.pdf']
    },
    maxSize,
    multiple: false,
    disabled: disabled || uploadState.status === 'processing'
  });

  const removeFile = () => {
    setUploadState({
      status: 'idle',
      file: null,
      result: null,
      progress: 0,
      error: null
    });
    onFileRemoved?.();
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getFileIcon = (filename: string) => {
    const extension = filename.split('.').pop()?.toLowerCase();
    switch (extension) {
      case 'pdf':
        return <File className="w-8 h-8 text-red-500" />;
      case 'docx':
        return <FileText className="w-8 h-8 text-blue-500" />;
      case 'txt':
        return <FileText className="w-8 h-8 text-gray-500" />;
      default:
        return <File className="w-8 h-8 text-gray-500" />;
    }
  };

  if (uploadState.status === 'success' && uploadState.file && uploadState.result) {
    return (
      <Card className="border-gray-200 bg-gray-50">
        <CardContent className="p-6">
          <div className="flex items-start space-x-4">
            <div className="flex-shrink-0">
              {getFileIcon(uploadState.file.name)}
            </div>
            <div className="flex-1 min-w-0">
              <div className="flex items-center space-x-2 mb-2">
                <h3 className="text-lg font-medium text-gray-900 truncate">
                  {uploadState.file.name}
                </h3>
                <Badge className="bg-black text-white">
                  <CheckCircle className="w-3 h-3 mr-1" />
                  已处理
                </Badge>
              </div>
              <div className="grid grid-cols-2 gap-4 text-sm text-gray-600 mb-4">
                <div>
                  <span className="font-medium">文件大小:</span> {formatFileSize(uploadState.file.size)}
                </div>
                <div>
                  <span className="font-medium">字数统计:</span> {uploadState.result.wordCount.toLocaleString()} 字
                </div>
              </div>
              <div className="text-sm text-gray-600 mb-4">
                <span className="font-medium">内容预览:</span>
                <div className="mt-1 p-3 bg-white rounded border text-xs leading-relaxed max-h-32 overflow-y-auto">
                  {uploadState.result.content.substring(0, 200)}
                  {uploadState.result.content.length > 200 && '...'}
                </div>
              </div>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={removeFile}
              className="flex-shrink-0"
            >
              <X className="w-4 h-4" />
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (uploadState.status === 'processing' && uploadState.file) {
    return (
      <Card className="border-gray-200 bg-gray-50">
        <CardContent className="p-6">
          <div className="flex items-center space-x-4">
            <div className="flex-shrink-0">
              <Loader2 className="w-8 h-8 text-gray-600 animate-spin" />
            </div>
            <div className="flex-1">
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                正在处理文件: {uploadState.file.name}
              </h3>
              <Progress value={uploadState.progress} className="mb-2" />
              <p className="text-sm text-gray-600">
                正在解析文件内容，请稍候...
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (uploadState.status === 'error') {
    return (
      <Card className="border-red-200 bg-red-50">
        <CardContent className="p-6">
          <div className="flex items-start space-x-4">
            <div className="flex-shrink-0">
              <AlertCircle className="w-8 h-8 text-red-500" />
            </div>
            <div className="flex-1">
              <h3 className="text-lg font-medium text-red-900 mb-2">
                文件处理失败
              </h3>
              <p className="text-sm text-red-700 mb-4">
                {uploadState.error}
              </p>
              <Button
                variant="outline"
                size="sm"
                onClick={removeFile}
                className="border-red-300 text-red-700 hover:bg-red-100"
              >
                重新上传
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="border-dashed border-2 border-gray-300 hover:border-gray-400 transition-colors">
      <CardContent className="p-0">
        <div
          {...getRootProps()}
          className={`p-12 text-center cursor-pointer transition-colors ${
            isDragActive ? 'bg-gray-50' : 'hover:bg-gray-50'
          } ${disabled ? 'cursor-not-allowed opacity-50' : ''}`}
        >
          <input {...getInputProps()} />
          <div className="mx-auto w-16 h-16 mb-4 flex items-center justify-center">
            <Upload className={`w-12 h-12 ${isDragActive ? 'text-gray-600' : 'text-gray-400'}`} />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            {isDragActive ? '释放文件以上传' : '上传小说文件'}
          </h3>
          <p className="text-gray-600 mb-4">
            拖拽文件到此处，或点击选择文件
          </p>
          <div className="flex flex-wrap justify-center gap-2 mb-4">
            {acceptedTypes.map(type => (
              <Badge key={type} variant="secondary">
                {type}
              </Badge>
            ))}
          </div>
          <p className="text-sm text-gray-500">
            最大文件大小: {Math.round(maxSize / 1024 / 1024)}MB
          </p>
        </div>
      </CardContent>
    </Card>
  );
}
