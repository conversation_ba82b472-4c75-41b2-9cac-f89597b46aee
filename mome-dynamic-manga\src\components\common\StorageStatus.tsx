'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { StorageManager } from '@/lib/storageManager';
import { 
  HardDrive, 
  Trash2, 
  RefreshCw, 
  AlertTriangle,
  CheckCircle,
  Info
} from 'lucide-react';

interface StorageStatusProps {
  showDetails?: boolean;
  className?: string;
}

export function StorageStatus({ showDetails = false, className = '' }: StorageStatusProps) {
  const [stats, setStats] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isClearing, setIsClearing] = useState(false);

  useEffect(() => {
    loadStats();

    // 调试：将StorageManager暴露到全局作用域
    if (typeof window !== 'undefined') {
      (window as any).StorageManager = StorageManager;
      console.log('StorageManager已暴露到全局作用域，可以在控制台中使用');
    }
  }, []);

  const loadStats = () => {
    try {
      const storageStats = StorageManager.getStorageStats();
      setStats(storageStats);
    } catch (error) {
      console.error('获取存储状态失败:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCleanup = async () => {
    setIsClearing(true);
    try {
      console.log('开始清理过期数据...');

      // 先尝试清理过期数据
      StorageManager.cleanup();

      // 检查清理效果
      let storageInfo = StorageManager.getStorageInfo();
      let usagePercent = (storageInfo.used / storageInfo.total) * 100;
      console.log(`清理过期数据后，存储使用率: ${usagePercent.toFixed(1)}%`);

      // 如果存储仍然不足，进行强制清理
      if (usagePercent > 80) {
        console.log('存储仍然不足，开始强制清理最旧的项目...');
        const deletedCount = StorageManager.forceCleanup(2048); // 释放2MB空间

        if (deletedCount > 0) {
          // 重新检查存储状态
          storageInfo = StorageManager.getStorageInfo();
          usagePercent = (storageInfo.used / storageInfo.total) * 100;
          alert(`已清理 ${deletedCount} 个最旧的项目，当前存储使用率: ${usagePercent.toFixed(1)}%`);
        } else {
          alert('没有找到可清理的项目');
        }
      } else {
        alert('清理完成！存储空间已释放');
      }

      loadStats(); // 重新加载统计信息
    } catch (error) {
      console.error('清理失败:', error);
      alert(`清理失败: ${error.message}`);
    } finally {
      setIsClearing(false);
    }
  };

  const handleClearAll = async () => {
    if (confirm('确定要删除所有项目数据吗？此操作不可恢复！')) {
      setIsClearing(true);
      try {
        StorageManager.clearAll();
        loadStats();
        alert('所有数据已清理完成');
      } catch (error) {
        console.error('清理所有数据失败:', error);
        alert('清理失败');
      } finally {
        setIsClearing(false);
      }
    }
  };

  if (isLoading) {
    return (
      <Card className={className}>
        <CardContent className="p-4">
          <div className="flex items-center space-x-2">
            <RefreshCw className="w-4 h-4 animate-spin" />
            <span className="text-sm text-gray-600">加载存储状态...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!stats) {
    return (
      <Card className={className}>
        <CardContent className="p-4">
          <div className="flex items-center space-x-2 text-red-600">
            <AlertTriangle className="w-4 h-4" />
            <span className="text-sm">无法获取存储状态</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  const usagePercent = parseFloat(stats.storage.usagePercent);
  const isNearFull = usagePercent > 80;
  const isFull = usagePercent > 95;

  if (!showDetails) {
    // 简化显示
    return (
      <div className={`flex items-center space-x-2 ${className}`}>
        <HardDrive className={`w-4 h-4 ${isFull ? 'text-red-500' : isNearFull ? 'text-yellow-500' : 'text-green-500'}`} />
        <span className="text-sm text-gray-600">
          存储: {stats.storage.used} / {stats.storage.total}
        </span>
        {(isNearFull || isFull) && (
          <Badge variant={isFull ? "destructive" : "secondary"} className="text-xs">
            {isFull ? '存储已满' : '存储不足'}
          </Badge>
        )}
      </div>
    );
  }

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center space-x-2 text-sm">
          <HardDrive className="w-4 h-4" />
          <span>存储状态</span>
          <Button
            variant="ghost"
            size="sm"
            onClick={loadStats}
            className="ml-auto p-1 h-6 w-6"
          >
            <RefreshCw className="w-3 h-3" />
          </Button>
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* 存储使用情况 */}
        <div>
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium">存储空间</span>
            <span className="text-sm text-gray-600">
              {stats.storage.used} / {stats.storage.total}
            </span>
          </div>
          
          <Progress 
            value={usagePercent} 
            className="h-2"
            // @ts-ignore
            indicatorClassName={
              isFull ? 'bg-red-500' : 
              isNearFull ? 'bg-yellow-500' : 
              'bg-green-500'
            }
          />
          
          <div className="flex items-center justify-between mt-1">
            <span className="text-xs text-gray-500">
              {usagePercent.toFixed(1)}% 已使用
            </span>
            <span className="text-xs text-gray-500">
              剩余 {stats.storage.available}
            </span>
          </div>
        </div>

        {/* 项目统计 */}
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <div className="text-gray-600">项目数量</div>
            <div className="font-medium">{stats.projects.count}</div>
          </div>
          <div>
            <div className="text-gray-600">数据大小</div>
            <div className="font-medium">
              {(stats.projects.totalSize / 1024).toFixed(1)} KB
            </div>
          </div>
        </div>

        {/* 状态提示 */}
        <div className="space-y-2">
          {isFull && (
            <div className="flex items-start space-x-2 p-2 bg-red-50 rounded-lg">
              <AlertTriangle className="w-4 h-4 text-red-500 mt-0.5" />
              <div className="text-sm">
                <div className="font-medium text-red-700">存储空间已满</div>
                <div className="text-red-600">无法保存新项目，请清理数据</div>
              </div>
            </div>
          )}
          
          {isNearFull && !isFull && (
            <div className="flex items-start space-x-2 p-2 bg-yellow-50 rounded-lg">
              <AlertTriangle className="w-4 h-4 text-yellow-500 mt-0.5" />
              <div className="text-sm">
                <div className="font-medium text-yellow-700">存储空间不足</div>
                <div className="text-yellow-600">建议清理过期数据</div>
              </div>
            </div>
          )}
          
          {!isNearFull && (
            <div className="flex items-start space-x-2 p-2 bg-green-50 rounded-lg">
              <CheckCircle className="w-4 h-4 text-green-500 mt-0.5" />
              <div className="text-sm">
                <div className="font-medium text-green-700">存储空间充足</div>
                <div className="text-green-600">可以正常创建新项目</div>
              </div>
            </div>
          )}
        </div>

        {/* 操作按钮 */}
        <div className="space-y-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleCleanup}
            disabled={isClearing}
            className="w-full"
          >
            {isClearing ? (
              <>
                <RefreshCw className="w-3 h-3 mr-1 animate-spin" />
                清理中...
              </>
            ) : (
              <>
                <Trash2 className="w-3 h-3 mr-1" />
                智能清理
              </>
            )}
          </Button>

          {(isFull || isNearFull) && (
            <Button
              variant="destructive"
              size="sm"
              onClick={handleClearAll}
              disabled={isClearing}
              className="w-full"
            >
              <Trash2 className="w-3 h-3 mr-1" />
              清理所有数据
            </Button>
          )}
        </div>

        {/* 帮助信息 */}
        <div className="text-xs text-gray-500 space-y-1">
          <div className="flex items-start space-x-1">
            <Info className="w-3 h-3 mt-0.5" />
            <div>
              <div>• 数据存储在浏览器本地</div>
              <div>• 清理浏览器缓存会删除所有数据</div>
              <div>• 建议定期导出重要项目</div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
