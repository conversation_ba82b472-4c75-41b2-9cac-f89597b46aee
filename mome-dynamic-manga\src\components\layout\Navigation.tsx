'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  FileText, 
  Upload, 
  Scissors, 
  Wand2, 
  Film, 
  Table,
  Home,
  ChevronRight
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface NavigationStep {
  id: number;
  title: string;
  description: string;
  path: string;
  icon: React.ComponentType<{ className?: string }>;
  status: 'completed' | 'current' | 'upcoming';
}

interface NavigationProps {
  currentStep?: number;
  projectId?: string;
}

export function Navigation({ currentStep = 0, projectId }: NavigationProps) {
  const pathname = usePathname();

  const steps: NavigationStep[] = [
    {
      id: 1,
      title: "上传小说",
      description: "导入长篇小说文本",
      path: "/upload",
      icon: Upload,
      status: currentStep === 1 ? 'current' : currentStep > 1 ? 'completed' : 'upcoming'
    },
    {
      id: 2,
      title: "章节切分",
      description: "智能识别章节结构",
      path: projectId ? `/chapters/${projectId}` : "/chapters",
      icon: Scissors,
      status: currentStep === 2 ? 'current' : currentStep > 2 ? 'completed' : 'upcoming'
    },
    {
      id: 3,
      title: "单章处理",
      description: "AI分析生成剧情设计",
      path: projectId ? `/story-design/${projectId}` : "/story-design",
      icon: Wand2,
      status: currentStep === 3 ? 'current' : currentStep > 3 ? 'completed' : 'upcoming'
    },
    {
      id: 4,
      title: "剧本生成",
      description: "生成动态漫剧本",
      path: projectId ? `/script/${projectId}` : "/script",
      icon: Film,
      status: currentStep === 4 ? 'current' : currentStep > 4 ? 'completed' : 'upcoming'
    },
    {
      id: 5,
      title: "分镜表",
      description: "编辑和导出分镜表",
      path: projectId ? `/storyboard/${projectId}` : "/storyboard",
      icon: Table,
      status: currentStep === 5 ? 'current' : currentStep > 5 ? 'completed' : 'upcoming'
    }
  ];

  return (
    <nav className="bg-white border-b border-gray-200">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-black rounded-lg flex items-center justify-center">
              <FileText className="w-5 h-5 text-white" />
            </div>
            <span className="text-xl font-semibold text-gray-900">MOME动态漫工具</span>
          </Link>

          {/* Navigation Steps - 简化版本 */}
          <div className="hidden md:flex items-center space-x-6">
            {steps.map((step, index) => {
              // 更准确的路径匹配 - 检查路径是否匹配步骤类型
              const stepType = step.path.split('/')[1]; // 获取路径的第二部分，如 'upload', 'chapters' 等
              const isActive = pathname.includes(`/${stepType}`);

              // 所有步骤都可访问：第一步总是可访问，其他步骤在有项目ID时可访问
              const isAccessible = step.id === 1 || projectId;

              return (
                <Link
                  key={step.id}
                  href={isAccessible ? step.path : '#'}
                  className={cn(
                    "flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium transition-colors",
                    isActive
                      ? "bg-black text-white"
                      : isAccessible
                      ? "text-gray-700 hover:text-black hover:bg-gray-100"
                      : "text-gray-400 cursor-not-allowed"
                  )}
                >
                  <span className={cn(
                    "w-6 h-6 rounded-full flex items-center justify-center text-xs font-semibold",
                    isActive
                      ? "bg-white text-black"
                      : step.status === 'completed'
                      ? "bg-black text-white"
                      : isAccessible
                      ? "bg-gray-200 text-gray-700"
                      : "bg-gray-100 text-gray-400"
                  )}>
                    {step.id}
                  </span>
                  <span>{step.title}</span>
                </Link>
              );
            })}
          </div>

          {/* Mobile Menu Button & Home Link */}
          <div className="flex items-center space-x-2">
            <Link href="/">
              <Button variant="ghost" size="sm">
                <Home className="w-4 h-4 mr-2" />
                首页
              </Button>
            </Link>
          </div>
        </div>

        {/* Mobile Navigation */}
        <div className="lg:hidden pb-4">
          <div className="flex space-x-2 overflow-x-auto">
            {steps.map((step) => {
              const Icon = step.icon;
              // 更准确的路径匹配 - 检查路径是否匹配步骤类型
              const stepType = step.path.split('/')[1]; // 获取路径的第二部分，如 'upload', 'chapters' 等
              const isActive = pathname.includes(`/${stepType}`);

              // 所有步骤都可访问：第一步总是可访问，其他步骤在有项目ID时可访问
              const isAccessible = step.id === 1 || projectId;

              return (
                <Link
                  key={step.id}
                  href={isAccessible ? step.path : '#'}
                  className={cn(
                    "flex flex-col items-center space-y-1 p-2 rounded-lg text-xs font-medium transition-colors min-w-[80px]",
                    isActive
                      ? "bg-blue-100 text-blue-700"
                      : isAccessible
                      ? "text-gray-600 hover:text-gray-900 hover:bg-gray-100"
                      : "text-gray-400 cursor-not-allowed"
                  )}
                >
                  <div className={cn(
                    "w-8 h-8 rounded-full flex items-center justify-center",
                    step.status === 'completed'
                      ? "bg-green-100 text-green-700"
                      : step.status === 'current'
                      ? "bg-blue-100 text-blue-700"
                      : "bg-gray-100 text-gray-400"
                  )}>
                    <Icon className="w-4 h-4" />
                  </div>
                  <span className="text-center leading-tight">{step.title}</span>
                  <Badge
                    variant={step.status === 'completed' ? 'default' : 'secondary'}
                    className="text-xs"
                  >
                    {step.id}
                  </Badge>
                </Link>
              );
            })}
          </div>
        </div>
      </div>
    </nav>
  );
}

// 进度指示器组件
export function ProgressIndicator({ currentStep, totalSteps = 5 }: { currentStep: number; totalSteps?: number }) {
  const progress = (currentStep / totalSteps) * 100;

  return (
    <div className="w-full bg-gray-200 rounded-full h-2">
      <div 
        className="bg-gradient-to-r from-blue-600 to-indigo-600 h-2 rounded-full transition-all duration-300"
        style={{ width: `${progress}%` }}
      />
    </div>
  );
}
