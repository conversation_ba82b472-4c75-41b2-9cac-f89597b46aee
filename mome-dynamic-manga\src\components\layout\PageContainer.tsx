import { ReactNode } from 'react';
import { Navigation, ProgressIndicator } from './Navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { ArrowLeft, ArrowRight } from 'lucide-react';
import Link from 'next/link';

interface PageContainerProps {
  children: ReactNode;
  title: string;
  description?: string;
  currentStep?: number;
  projectId?: string;
  showProgress?: boolean;
  previousStep?: {
    title: string;
    href: string;
  };
  nextStep?: {
    title: string;
    href: string;
    disabled?: boolean;
  };
  actions?: ReactNode;
}

export function PageContainer({
  children,
  title,
  description,
  currentStep = 0,
  projectId,
  showProgress = true,
  previousStep,
  nextStep,
  actions
}: PageContainerProps) {
  return (
    <div className="min-h-screen bg-gray-50">
      <Navigation currentStep={currentStep} projectId={projectId} />
      
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Page Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-4">
            <div>
              <div className="flex items-center space-x-3 mb-2">
                <h1 className="text-3xl font-bold text-gray-900">{title}</h1>
                {currentStep > 0 && (
                  <Badge variant="outline">
                    Step {currentStep}
                  </Badge>
                )}
              </div>
              {description && (
                <p className="text-lg text-gray-600">{description}</p>
              )}
            </div>
            {actions && (
              <div className="flex items-center space-x-2">
                {actions}
              </div>
            )}
          </div>

          {/* Progress Indicator */}
          {showProgress && currentStep > 0 && (
            <div className="mb-6">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-gray-700">
                  进度: {currentStep}/5
                </span>
                <span className="text-sm text-gray-500">
                  {Math.round((currentStep / 5) * 100)}% 完成
                </span>
              </div>
              <ProgressIndicator currentStep={currentStep} />
            </div>
          )}
        </div>

        {/* Main Content */}
        <div className="space-y-6">
          {children}
        </div>

        {/* Navigation Footer */}
        {(previousStep || nextStep) && (
          <div className="mt-12 pt-8 border-t border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                {previousStep && (
                  <Link href={previousStep.href}>
                    <Button variant="outline" className="flex items-center space-x-2">
                      <ArrowLeft className="w-4 h-4" />
                      <span>上一步: {previousStep.title}</span>
                    </Button>
                  </Link>
                )}
              </div>
              <div>
                {nextStep && (
                  <Link href={nextStep.disabled ? '#' : nextStep.href}>
                    <Button 
                      disabled={nextStep.disabled}
                      className="flex items-center space-x-2"
                    >
                      <span>下一步: {nextStep.title}</span>
                      <ArrowRight className="w-4 h-4" />
                    </Button>
                  </Link>
                )}
              </div>
            </div>
          </div>
        )}
      </main>
    </div>
  );
}

// 步骤卡片组件
interface StepCardProps {
  title: string;
  description: string;
  children: ReactNode;
  status?: 'pending' | 'processing' | 'completed' | 'error';
  className?: string;
}

export function StepCard({ 
  title, 
  description, 
  children, 
  status = 'pending',
  className = ''
}: StepCardProps) {
  const getStatusColor = () => {
    switch (status) {
      case 'completed':
        return 'border-green-200 bg-green-50';
      case 'processing':
        return 'border-blue-200 bg-blue-50';
      case 'error':
        return 'border-red-200 bg-red-50';
      default:
        return 'border-gray-200 bg-white';
    }
  };

  const getStatusBadge = () => {
    switch (status) {
      case 'completed':
        return <Badge className="bg-green-100 text-green-800">已完成</Badge>;
      case 'processing':
        return <Badge className="bg-blue-100 text-blue-800">处理中</Badge>;
      case 'error':
        return <Badge variant="destructive">错误</Badge>;
      default:
        return <Badge variant="secondary">待处理</Badge>;
    }
  };

  return (
    <Card className={`${getStatusColor()} ${className}`}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-xl">{title}</CardTitle>
            <CardDescription className="mt-1">{description}</CardDescription>
          </div>
          {getStatusBadge()}
        </div>
      </CardHeader>
      <CardContent>
        {children}
      </CardContent>
    </Card>
  );
}

// 空状态组件
interface EmptyStateProps {
  title: string;
  description: string;
  icon?: ReactNode;
  action?: ReactNode;
}

export function EmptyState({ title, description, icon, action }: EmptyStateProps) {
  return (
    <div className="text-center py-12">
      {icon && (
        <div className="mx-auto w-16 h-16 mb-4 flex items-center justify-center text-gray-400">
          {icon}
        </div>
      )}
      <h3 className="text-lg font-medium text-gray-900 mb-2">{title}</h3>
      <p className="text-gray-600 mb-6 max-w-md mx-auto">{description}</p>
      {action && action}
    </div>
  );
}

// 加载状态组件
interface LoadingStateProps {
  title?: string;
  description?: string;
}

export function LoadingState({ 
  title = "加载中...", 
  description = "请稍候，正在处理您的请求" 
}: LoadingStateProps) {
  return (
    <div className="text-center py-12">
      <div className="mx-auto w-16 h-16 mb-4">
        <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600"></div>
      </div>
      <h3 className="text-lg font-medium text-gray-900 mb-2">{title}</h3>
      <p className="text-gray-600">{description}</p>
    </div>
  );
}

// 错误状态组件
interface ErrorStateProps {
  title?: string;
  description?: string;
  action?: ReactNode;
}

export function ErrorState({ 
  title = "出现错误", 
  description = "抱歉，处理过程中出现了问题",
  action 
}: ErrorStateProps) {
  return (
    <div className="text-center py-12">
      <div className="mx-auto w-16 h-16 mb-4 flex items-center justify-center">
        <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center">
          <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
          </svg>
        </div>
      </div>
      <h3 className="text-lg font-medium text-gray-900 mb-2">{title}</h3>
      <p className="text-gray-600 mb-6">{description}</p>
      {action && action}
    </div>
  );
}
