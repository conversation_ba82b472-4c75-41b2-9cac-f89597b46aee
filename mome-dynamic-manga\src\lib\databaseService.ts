import {
  supabase,
  TABLES,
  Project,
  Chapter,
  StoryDesign,
  Script,
  Storyboard,
  handleDatabaseError
} from './supabase';

// 检查数据库客户端是否可用
function ensureSupabaseClient() {
  if (!supabase) {
    throw new Error('数据库连接失败：Supabase 环境变量未正确配置。请检查 .env.local 文件中的 NEXT_PUBLIC_SUPABASE_URL 和 NEXT_PUBLIC_SUPABASE_ANON_KEY');
  }
  return supabase;
}

// 项目相关操作
export class ProjectService {
  // 创建项目
  static async create(projectData: Omit<Project, 'created_at' | 'updated_at'>): Promise<Project> {
    try {
      const client = ensureSupabaseClient();
      const { data, error } = await client
        .from(TABLES.PROJECTS)
        .insert(projectData)
        .select()
        .single();

      if (error) {
        handleDatabaseError(error, '创建项目');
      }

      return data;
    } catch (error) {
      handleDatabaseError(error, '创建项目');
    }
  }

  // 获取项目
  static async getById(id: string): Promise<Project | null> {
    try {
      const client = ensureSupabaseClient();
      const { data, error } = await client
        .from(TABLES.PROJECTS)
        .select('*')
        .eq('id', id)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          return null; // 项目不存在
        }
        handleDatabaseError(error, '获取项目');
      }

      return data;
    } catch (error) {
      handleDatabaseError(error, '获取项目');
      return null;
    }
  }

  // 获取所有项目
  static async getAll(): Promise<Project[]> {
    try {
      const client = ensureSupabaseClient();
      const { data, error } = await client
        .from(TABLES.PROJECTS)
        .select('*')
        .order('created_at', { ascending: false });

      if (error) {
        handleDatabaseError(error, '获取项目列表');
      }

      return data || [];
    } catch (error) {
      handleDatabaseError(error, '获取项目列表');
      return [];
    }
  }

  // 删除项目
  static async delete(id: string): Promise<void> {
    try {
      const client = ensureSupabaseClient();
      const { error } = await client
        .from(TABLES.PROJECTS)
        .delete()
        .eq('id', id);

      if (error) {
        handleDatabaseError(error, '删除项目');
      }
    } catch (error) {
      handleDatabaseError(error, '删除项目');
    }
  }

  // 更新项目
  static async update(id: string, updates: Partial<Project>): Promise<Project> {
    try {
      const { data, error } = await supabase
        .from(TABLES.PROJECTS)
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (error) {
        handleDatabaseError(error, '更新项目');
      }

      return data;
    } catch (error) {
      handleDatabaseError(error, '更新项目');
    }
  }

  // 删除项目
  static async delete(id: string): Promise<void> {
    try {
      const { error } = await supabase
        .from(TABLES.PROJECTS)
        .delete()
        .eq('id', id);

      if (error) {
        handleDatabaseError(error, '删除项目');
      }
    } catch (error) {
      handleDatabaseError(error, '删除项目');
    }
  }
}

// 章节相关操作
export class ChapterService {
  // 批量创建章节
  static async createBatch(chapters: Omit<Chapter, 'created_at' | 'updated_at'>[]): Promise<Chapter[]> {
    try {
      const client = ensureSupabaseClient();

      // 首次尝试插入完整数据
      let { data, error } = await client
        .from(TABLES.CHAPTERS)
        .insert(chapters)
        .select();

      // 如果遇到字段不存在错误，尝试使用基础字段
      if (error && error.message.includes('end_index')) {
        console.warn('检测到数据库缺少索引字段，使用基础字段重试...');

        const basicChapters = chapters.map(chapter => {
          const { start_index, end_index, ...basicChapter } = chapter as any;
          return basicChapter;
        });

        const { data: retryData, error: retryError } = await client
          .from(TABLES.CHAPTERS)
          .insert(basicChapters)
          .select();

        if (retryError) {
          handleDatabaseError(retryError, '创建章节（重试）');
        }

        data = retryData;
        error = retryError;
      }

      if (error) {
        handleDatabaseError(error, '创建章节');
      }

      return data || [];
    } catch (error) {
      handleDatabaseError(error, '创建章节');
    }
  }

  // 获取项目的所有章节
  static async getByProjectId(projectId: string): Promise<Chapter[]> {
    try {
      const client = ensureSupabaseClient();
      const { data, error } = await client
        .from(TABLES.CHAPTERS)
        .select('*')
        .eq('project_id', projectId)
        .order('chapter_number', { ascending: true });

      if (error) {
        handleDatabaseError(error, '获取章节列表');
      }

      return data || [];
    } catch (error) {
      handleDatabaseError(error, '获取章节列表');
    }
  }

  // 更新章节
  static async update(id: string, updates: Partial<Chapter>): Promise<Chapter> {
    try {
      const client = ensureSupabaseClient();
      const { data, error } = await client
        .from(TABLES.CHAPTERS)
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (error) {
        handleDatabaseError(error, '更新章节');
      }

      return data;
    } catch (error) {
      handleDatabaseError(error, '更新章节');
    }
  }

  // 删除单个章节
  static async delete(id: string): Promise<void> {
    try {
      const client = ensureSupabaseClient();
      const { error } = await client
        .from(TABLES.CHAPTERS)
        .delete()
        .eq('id', id);

      if (error) {
        handleDatabaseError(error, '删除章节');
      }
    } catch (error) {
      handleDatabaseError(error, '删除章节');
    }
  }

  // 删除项目的所有章节
  static async deleteByProjectId(projectId: string): Promise<void> {
    try {
      const { error } = await supabase
        .from(TABLES.CHAPTERS)
        .delete()
        .eq('project_id', projectId);

      if (error) {
        handleDatabaseError(error, '删除章节');
      }
    } catch (error) {
      handleDatabaseError(error, '删除章节');
    }
  }
}

// 剧情设计相关操作
export class StoryDesignService {
  // 创建剧情设计
  static async create(storyDesign: Omit<StoryDesign, 'created_at' | 'updated_at'>): Promise<StoryDesign> {
    try {
      const client = ensureSupabaseClient();

      console.log('🔍 准备保存剧情设计:', storyDesign);

      const { data, error } = await client
        .from(TABLES.STORY_DESIGNS)
        .insert(storyDesign)
        .select()
        .single();

      if (error) {
        console.error('❌ 数据库错误:', error);
        handleDatabaseError(error, '创建剧情设计');
      }

      console.log('✅ 剧情设计保存成功:', data);
      return data;
    } catch (error) {
      console.error('❌ 创建剧情设计异常:', error);
      handleDatabaseError(error, '创建剧情设计');
    }
  }

  // 获取项目的剧情设计
  static async getByProjectId(projectId: string): Promise<StoryDesign[]> {
    try {
      const client = ensureSupabaseClient();
      const { data, error } = await client
        .from(TABLES.STORY_DESIGNS)
        .select('*')
        .eq('project_id', projectId)
        .order('created_at', { ascending: false });

      if (error) {
        handleDatabaseError(error, '获取剧情设计');
      }

      return data || [];
    } catch (error) {
      handleDatabaseError(error, '获取剧情设计');
      return [];
    }
  }

  // 更新剧情设计
  static async update(id: string, updates: Partial<StoryDesign>): Promise<StoryDesign> {
    try {
      const { data, error } = await supabase
        .from(TABLES.STORY_DESIGNS)
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (error) {
        handleDatabaseError(error, '更新剧情设计');
      }

      return data;
    } catch (error) {
      handleDatabaseError(error, '更新剧情设计');
    }
  }
}

// 剧本相关操作
export class ScriptService {
  // 创建剧本
  static async create(script: Omit<Script, 'created_at' | 'updated_at'>): Promise<Script> {
    try {
      const client = ensureSupabaseClient();
      const { data, error } = await client
        .from(TABLES.SCRIPTS)
        .insert(script)
        .select()
        .single();

      if (error) {
        handleDatabaseError(error, '创建剧本');
      }

      return data;
    } catch (error) {
      handleDatabaseError(error, '创建剧本');
    }
  }

  // 获取项目的剧本
  static async getByProjectId(projectId: string): Promise<Script[]> {
    try {
      const client = ensureSupabaseClient();
      const { data, error } = await client
        .from(TABLES.SCRIPTS)
        .select('*')
        .eq('project_id', projectId)
        .order('created_at', { ascending: false });

      if (error) {
        handleDatabaseError(error, '获取剧本');
      }

      return data || [];
    } catch (error) {
      handleDatabaseError(error, '获取剧本');
      return [];
    }
  }

  // 更新剧本
  static async update(id: string, updates: Partial<Script>): Promise<Script> {
    try {
      const { data, error } = await supabase
        .from(TABLES.SCRIPTS)
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (error) {
        handleDatabaseError(error, '更新剧本');
      }

      return data;
    } catch (error) {
      handleDatabaseError(error, '更新剧本');
    }
  }
}

// 分镜相关操作
export class StoryboardService {
  // 创建分镜
  static async create(storyboard: Omit<Storyboard, 'created_at' | 'updated_at'>): Promise<Storyboard> {
    try {
      const client = ensureSupabaseClient();
      const { data, error } = await client
        .from(TABLES.STORYBOARDS)
        .insert(storyboard)
        .select()
        .single();

      if (error) {
        handleDatabaseError(error, '创建分镜');
      }

      return data;
    } catch (error) {
      handleDatabaseError(error, '创建分镜');
    }
  }

  // 获取项目的分镜
  static async getByProjectId(projectId: string): Promise<Storyboard[]> {
    try {
      const client = ensureSupabaseClient();
      const { data, error } = await client
        .from(TABLES.STORYBOARDS)
        .select('*')
        .eq('project_id', projectId)
        .order('created_at', { ascending: false });

      if (error) {
        handleDatabaseError(error, '获取分镜');
      }

      return data || [];
    } catch (error) {
      handleDatabaseError(error, '获取分镜');
      return [];
    }
  }

  // 更新分镜
  static async update(id: string, updates: Partial<Storyboard>): Promise<Storyboard> {
    try {
      const { data, error } = await supabase
        .from(TABLES.STORYBOARDS)
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (error) {
        handleDatabaseError(error, '更新分镜');
      }

      return data;
    } catch (error) {
      handleDatabaseError(error, '更新分镜');
    }
  }
}
