import { FileParseResult, FileValidationResult } from '@/types';

export class FileParser {
  static async parseFile(file: File): Promise<FileParseResult> {
    try {
      const extension = file.name.split('.').pop()?.toLowerCase();
      let content: string;

      switch (extension) {
        case 'txt':
          content = await this.parseTxt(file);
          break;
        case 'docx':
          content = await this.parseDocx(file);
          break;
        case 'pdf':
          content = await this.parsePdf(file);
          break;
        default:
          throw new Error('不支持的文件格式。请上传 .txt, .docx 或 .pdf 文件。');
      }

      // 计算字数（中文字符和英文单词）
      const wordCount = this.calculateWordCount(content);

      return {
        success: true,
        filename: file.name,
        content: content.trim(),
        wordCount,
      };
    } catch (error) {
      return {
        success: false,
        filename: file.name,
        content: '',
        wordCount: 0,
        error: error instanceof Error ? error.message : '文件解析失败',
      };
    }
  }

  private static async parseTxt(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();

      reader.onload = (e) => {
        let content = e.target?.result as string;

        // 检查是否有乱码字符
        if (content && content.includes('�')) {
          // 尝试使用ArrayBuffer重新读取
          const reader2 = new FileReader();
          reader2.onload = (e2) => {
            const arrayBuffer = e2.target?.result as ArrayBuffer;

            // 尝试不同的编码
            const encodings = ['gbk', 'gb2312', 'big5'];
            let decodedContent = content;

            for (const encoding of encodings) {
              try {
                const decoder = new TextDecoder(encoding);
                const testContent = decoder.decode(arrayBuffer);
                // 如果解码后没有乱码字符，使用这个结果
                if (!testContent.includes('�')) {
                  decodedContent = testContent;
                  break;
                }
              } catch {
                // 继续尝试下一个编码
                continue;
              }
            }

            resolve(decodedContent);
          };
          reader2.onerror = () => reject(new Error('TXT文件读取失败'));
          reader2.readAsArrayBuffer(file);
        } else {
          resolve(content);
        }
      };

      reader.onerror = () => reject(new Error('TXT文件读取失败'));
      reader.readAsText(file, 'utf-8');
    });
  }

  private static async parseDocx(file: File): Promise<string> {
    // 注意：这里需要在客户端使用mammoth
    // 由于mammoth在浏览器环境中的使用方式不同，我们需要动态导入
    try {
      const mammoth = await import('mammoth');
      const arrayBuffer = await file.arrayBuffer();
      const result = await mammoth.extractRawText({ arrayBuffer });
      return result.value;
    } catch (error) {
      throw new Error('DOCX文件解析失败，请确保文件格式正确');
    }
  }

  private static async parsePdf(file: File): Promise<string> {
    console.log('🔍 开始PDF解析流程...');
    console.log('📄 文件信息:', {
      name: file.name,
      size: file.size,
      type: file.type,
      lastModified: new Date(file.lastModified).toISOString()
    });

    // 检查文件大小，避免内存溢出
    const maxSize = 50 * 1024 * 1024; // 50MB限制
    if (file.size > maxSize) {
      throw new Error(`PDF文件过大（${Math.round(file.size / 1024 / 1024)}MB），请选择小于50MB的文件`);
    }

    // 直接使用pdfjs-dist库（客户端解析）
    return await this.parsePdfWithPdfjs(file);
  }

  // 使用pdfjs-dist库（客户端解析）
  private static async parsePdfWithPdfjs(file: File): Promise<string> {
    try {
      console.log('📦 正在导入pdfjs-dist...');
      const pdfjsLib = await import('pdfjs-dist');
      console.log('✅ pdfjs-dist导入成功，版本:', pdfjsLib.version);

      // 配置PDF.js worker
      if (typeof window !== 'undefined') {
        try {
          // 尝试多个CDN源
          const workerSources = [
            `https://unpkg.com/pdfjs-dist@${pdfjsLib.version}/build/pdf.worker.min.js`,
            `https://cdn.jsdelivr.net/npm/pdfjs-dist@${pdfjsLib.version}/build/pdf.worker.min.js`,
            'https://unpkg.com/pdfjs-dist@4.0.379/build/pdf.worker.min.js'
          ];

          pdfjsLib.GlobalWorkerOptions.workerSrc = workerSources[0];
          console.log('🔧 Worker路径已设置:', workerSources[0]);
          console.log('📦 PDF.js版本:', pdfjsLib.version);
        } catch (error) {
          console.warn('⚠️ Worker设置失败:', error);
          // 禁用worker作为备用方案
          try {
            pdfjsLib.GlobalWorkerOptions.workerSrc = '';
            console.log('🔧 已禁用Worker，使用主线程模式');
          } catch (e) {
            console.warn('⚠️ 无法禁用Worker:', e);
          }
        }
      }

      console.log('📖 正在读取文件内容...');
      const arrayBuffer = await file.arrayBuffer();
      console.log('✅ 文件读取成功，ArrayBuffer大小:', arrayBuffer.byteLength);

      console.log('🔄 正在解析PDF文档...');

      // 尝试多种配置方式
      let pdf;
      const configs = [
        // 配置1: 使用worker
        {
          data: arrayBuffer,
          verbosity: 0,
          isEvalSupported: false,
          disableWorker: false
        },
        // 配置2: 禁用worker
        {
          data: arrayBuffer,
          verbosity: 0,
          isEvalSupported: false,
          disableWorker: true
        },
        // 配置3: 最小配置
        {
          data: arrayBuffer
        }
      ];

      for (let i = 0; i < configs.length; i++) {
        try {
          console.log(`🔄 尝试配置 ${i + 1}...`);
          pdf = await pdfjsLib.getDocument(configs[i]).promise;
          console.log(`✅ 配置 ${i + 1} 成功`);
          break;
        } catch (error) {
          console.warn(`❌ 配置 ${i + 1} 失败:`, error);
          if (i === configs.length - 1) {
            throw error;
          }
        }
      }

      if (!pdf) {
        throw new Error('PDF文档加载失败');
      }

      let fullText = '';
      console.log('PDF页面数量:', pdf.numPages);

      // 遍历所有页面
      for (let pageNum = 1; pageNum <= pdf.numPages; pageNum++) {
        try {
          console.log(`正在处理第 ${pageNum} 页...`);
          const page = await pdf.getPage(pageNum);
          const textContent = await page.getTextContent();

          // 提取文本并保持更好的格式
          const pageText = textContent.items
            .map((item: any) => {
              // 检查item是否有str属性
              if (item && typeof item.str === 'string') {
                return item.str;
              }
              return '';
            })
            .filter(text => text.trim().length > 0) // 过滤空字符串
            .join(' ');

          if (pageText.trim()) {
            fullText += pageText + '\n\n'; // 页面间添加双换行
          }

          console.log(`第 ${pageNum} 页处理完成，提取文本长度:`, pageText.length);
        } catch (pageError) {
          console.warn(`处理第 ${pageNum} 页时出错:`, pageError);
          // 继续处理下一页，不中断整个过程
          continue;
        }
      }

      console.log('PDF解析完成，总文本长度:', fullText.length);

      if (!fullText.trim()) {
        throw new Error('PDF文件中没有找到可提取的文本内容。这可能是因为：\n1. PDF是扫描版图片格式\n2. PDF有密码保护\n3. PDF文件损坏\n\n建议：请尝试将PDF转换为TXT格式后重新上传');
      }

      return fullText.trim();
    } catch (error) {
      console.error('PDF解析错误详情:', error);

      if (error instanceof Error) {
        // 根据不同的错误类型提供更具体的建议
        if (error.message.includes('Invalid PDF')) {
          throw new Error('PDF文件格式无效或已损坏。请检查文件是否完整，或尝试使用其他PDF文件。');
        } else if (error.message.includes('password')) {
          throw new Error('PDF文件受密码保护。请先移除密码保护后重新上传。');
        } else if (error.message.includes('worker')) {
          throw new Error('PDF解析器加载失败。请刷新页面重试，或检查网络连接。');
        } else if (error.message.includes('没有找到可提取的文本内容')) {
          // 保持原有的详细错误信息
          throw error;
        } else {
          throw new Error(`PDF文件解析失败: ${error.message}\n\n建议：\n1. 确保PDF文件完整且未损坏\n2. 尝试将PDF转换为TXT格式\n3. 检查PDF是否为扫描版（图片格式）`);
        }
      } else {
        throw new Error('PDF文件解析失败，发生未知错误。建议尝试转换为TXT格式后重新上传。');
      }
    }
  }

  private static calculateWordCount(text: string): number {
    // 移除多余的空白字符
    const cleanText = text.replace(/\s+/g, ' ').trim();
    
    // 分别计算中文字符和英文单词
    const chineseChars = (cleanText.match(/[\u4e00-\u9fff]/g) || []).length;
    const englishWords = (cleanText.match(/[a-zA-Z]+/g) || []).length;
    
    // 返回总字数（中文字符 + 英文单词）
    return chineseChars + englishWords;
  }

  // 验证文件类型和大小
  static validateFile(file: File): FileValidationResult {
    const maxSize = 20 * 1024 * 1024; // 20MB (降低限制以避免性能问题)
    const allowedTypes = ['txt', 'docx', 'pdf'];
    const extension = file.name.split('.').pop()?.toLowerCase();

    if (!extension || !allowedTypes.includes(extension)) {
      return {
        valid: false,
        error: '不支持的文件格式。请上传 .txt, .docx 或 .pdf 文件。',
      };
    }

    if (file.size > maxSize) {
      return {
        valid: false,
        error: '文件大小超过限制（最大20MB）。建议将大文件分割后上传。',
      };
    }

    // 对于TXT文件，额外检查是否可能包含二进制内容
    if (extension === 'txt' && file.size > 5 * 1024 * 1024) {
      // 大于5MB的TXT文件给出警告
      console.warn('检测到大型TXT文件，可能影响解析性能');
    }

    return { valid: true };
  }

  // 估算章节数量
  static estimateChapterCount(content: string): number {
    const patterns = [
      /第[一二三四五六七八九十\d]+章/g,
      /Chapter\s+\d+/gi,
      /第\d+话/g,
      /第\d+集/g,
    ];

    let maxCount = 0;
    for (const pattern of patterns) {
      const matches = content.match(pattern);
      if (matches && matches.length > maxCount) {
        maxCount = matches.length;
      }
    }

    // 如果没有找到明显的章节标记，根据字数估算
    if (maxCount === 0) {
      const wordCount = this.calculateWordCount(content);
      // 假设每章平均3000字
      maxCount = Math.max(1, Math.ceil(wordCount / 3000));
    }

    return maxCount;
  }
}
