import { createClient } from '@supabase/supabase-js';

// Supabase 配置
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

// 调试信息
console.log('Supabase 环境变量检查:', {
  url: supabaseUrl ? '已设置' : '未设置',
  key: supabaseAnonKey ? '已设置' : '未设置',
  urlValue: supabaseUrl?.substring(0, 20) + '...',
  keyValue: supabaseAnonKey?.substring(0, 20) + '...'
});

// 创建 Supabase 客户端（如果环境变量可用）
let supabase: any = null;
let supabaseAdmin: any = null;

if (supabaseUrl && supabaseAnonKey) {
  supabase = createClient(supabaseUrl, supabaseAnonKey);

  // 服务端使用的客户端（具有更高权限）
  if (process.env.SUPABASE_SERVICE_ROLE_KEY) {
    supabaseAdmin = createClient(supabaseUrl, process.env.SUPABASE_SERVICE_ROLE_KEY);
  }
} else {
  console.warn('Supabase 环境变量未设置，客户端将为 null');
}

export { supabase, supabaseAdmin };



// 数据库表名常量
export const TABLES = {
  PROJECTS: 'projects',
  CHAPTERS: 'chapters',
  STORY_DESIGNS: 'story_designs',
  SCRIPTS: 'scripts',
  STORYBOARDS: 'storyboards',
} as const;

// 类型定义 - 更新以匹配新的数据库结构
export interface Project {
  id: string;
  name: string;
  description?: string;
  original_content: string;
  total_words?: number;
  estimated_chapters?: number;
  status: 'uploaded' | 'chapters_split' | 'story_designed' | 'script_generated' | 'storyboard_completed';
  created_at: string;
  updated_at: string;
}

export interface Chapter {
  id: string;
  project_id: string;
  chapter_number: number;
  title: string;
  content: string;
  word_count: number;
  created_at: string;
}

export interface StoryDesign {
  id: string;
  project_id: string;
  chapter_id?: string;
  plot_summary: string;
  core_conflict: string;
  character_analysis: string;
  emotional_arc: string;
  visual_style: string;
  key_scenes: Array<{
    sceneNumber: number;
    description: string;
    emotion: string;
    visualElements: string;
  }>;
  adaptation_notes?: string;
  ai_model: string;
  created_at: string;
  updated_at: string;
}

export interface ScriptScene {
  sceneNumber: number;
  location: string;
  timeOfDay: string;
  characters: string[];
  dialogue: Array<{
    character: string;
    text: string;
    emotion: string;
    action?: string;
  }>;
  description: string;
  duration: string;
  visualDirection: string;
  cameraAngle: string;
  mood: string;
}

export interface Script {
  id: string;
  project_id: string;
  title: string;
  summary?: string;
  scenes: ScriptScene[];
  storyboard: any[];
  total_scenes: number;
  estimated_duration: number;
  ai_model: string;
  created_at: string;
  updated_at: string;
}

export interface StoryboardPanel {
  id: number;
  sceneNumber: number;
  panelNumber: number;
  description: string;
  dialogue: string;
  cameraAngle: string;
  composition: string;
}

export interface Storyboard {
  id: string;
  project_id: string;
  script_id?: string;
  panels: StoryboardPanel[];
  total_panels: number;
  total_images?: number; // 图片数量统计
  style_settings: {
    style?: string;
    colorScheme?: string;
    [key: string]: any;
  };
  created_at: string;
  updated_at: string;
}

// 数据库操作辅助函数
export class DatabaseError extends Error {
  constructor(message: string, public originalError?: any) {
    super(message);
    this.name = 'DatabaseError';
  }
}

// 通用错误处理
export function handleDatabaseError(error: any, operation: string): never {
  console.error(`数据库操作失败 (${operation}):`, error);

  if (error?.message?.includes('JWT')) {
    throw new DatabaseError('数据库认证失败，请检查API密钥配置');
  }

  if (error?.message?.includes('network')) {
    throw new DatabaseError('网络连接失败，请检查网络连接');
  }

  if (error?.code === 'PGRST116') {
    throw new DatabaseError('数据库表不存在，请检查数据库设置');
  }

  throw new DatabaseError(
    error?.message || `${operation}操作失败`,
    error
  );
}

// 测试数据库连接
export async function testConnection(): Promise<boolean> {
  try {
    // 首先检查 Supabase 客户端是否可用
    if (!supabase) {
      console.log('Supabase 客户端未初始化，环境变量可能未设置');
      return false;
    }

    const { data, error } = await supabase
      .from(TABLES.PROJECTS)
      .select('count')
      .limit(1);

    if (error) {
      console.error('数据库连接测试失败:', error);
      return false;
    }

    console.log('数据库连接测试成功');
    return true;
  } catch (error) {
    console.error('数据库连接测试异常:', error);
    return false;
  }
}
