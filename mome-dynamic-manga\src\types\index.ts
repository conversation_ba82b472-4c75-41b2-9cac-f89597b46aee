// 项目相关类型
export interface Project {
  id: string;
  name: string;
  description?: string;
  original_filename: string;
  file_url: string;
  original_content?: string; // 添加原文内容字段
  content?: string; // 兼容性字段，指向original_content
  total_words: number;
  estimated_chapters: number;
  status: 'uploaded' | 'processing' | 'completed' | 'error';
  created_at: string;
  updated_at: string;
}

// 章节相关类型
export interface Chapter {
  id: string;
  project_id: string;
  chapter_number: number;
  title: string;
  content: string;
  word_count: number;
  start_index: number;
  end_index: number;
  status?: 'pending' | 'processing' | 'completed';
  created_at: string;
}

export interface ChapterSuggestion {
  chapterNumber: number;
  title: string;
  content: string;
  startPosition: number;
  endPosition: number;
  wordCount: number;
  confidence: number;
}

// 剧情设计相关类型（与数据库表结构匹配）
export interface StoryDesign {
  id: string;
  project_id: string;
  chapter_id?: string;
  plot_summary: string;
  core_conflict: string;
  character_analysis: string;
  emotional_arc: string;
  visual_style: string;
  key_scenes?: any[];
  adaptation_notes?: string;
  ai_model?: string;
  created_at?: string;
  updated_at?: string;
}

// 剧本相关类型
export interface DramaScript {
  id: string;
  story_design_id: string;
  script_content: string;
  scene_count: number;
  created_at: string;
}

// 分镜相关类型
export interface StoryboardItem {
  id: string;
  drama_script_id: string;
  scene_num: string;
  location: string;
  scene_detail: string;
  dialogue: string;
  shot_type: string;
  visual_prompt: string;
  image_url?: string;
  sort_order: number;
  created_at: string;
}

// AI处理相关类型
export interface AIProcessingConfig {
  model: 'gemini-2.5-flash-lite' | 'gemini-2.0-flash-exp' | 'llama-4-maverick';
  temperature: number;
  max_tokens: number;
}

// 新的剧情设计类型（与aiProcessor.ts保持一致）
export interface StoryDesignNew {
  plotSummary: string;
  coreConflict: string;
  characterAnalysis: string;
  emotionalArc: string;
  visualStyle: string;
  keyScenes: Array<{
    sceneNumber: number;
    description: string;
    emotion: string;
    visualElements: string;
  }>;
  adaptationNotes: string;
}

export interface ScriptResult {
  scriptContent: string;
  storyboards: Omit<StoryboardItem, 'id' | 'drama_script_id' | 'created_at'>[];
  sceneCount: number;
}

// API响应类型
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// 文件上传相关类型
export interface FileUploadResult {
  success: boolean;
  filename: string;
  content: string;
  wordCount: number;
  error?: string;
}

// 导航相关类型
export interface NavigationStep {
  id: number;
  title: string;
  description: string;
  path: string;
  completed: boolean;
  current: boolean;
}
